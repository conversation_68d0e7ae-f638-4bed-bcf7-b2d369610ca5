#include <sys/mman.h>
#include <unistd.h>
#include "base.h"
#include "RtThread.h"
#include "rttimer.h"
#include "MainWork.h"
#include <pthread.h>
#include "SysShareMemoryOp.h"
#include "SysShareMemoryDefine.h"
#include  <stdio.h>
#include "SysVarDefine.h"

#include "DataBaseThread.h"
#include "EoThread.h"
#include "FieldbusThread.h"
#include "FileClear.h"
#include "FileServer.h"
#include "LogThread.h"
#include "PressThread.h"
#include "ServerMain.h"
#include <sqlite3.h>
pthread_t StartThread(const char* szThreadName, bool bPreempt, int thread_priority, void* (*__start_routine) (void*)) {
    pthread_t tid = 0;
    struct sched_param regparam = { .sched_priority = thread_priority };
    pthread_attr_t regattr;

    //��ʼ���߳����� 
    pthread_attr_init(&regattr);

    //�����߳�Ϊ�Ƿ����߳�    //ֻ�е��߳�pthread_joinʱ����ֹ�Լ�
    pthread_attr_setdetachstate(&regattr, PTHREAD_CREATE_JOINABLE);

    //�����̲߳��̳и��̵߳ĵ��Ȳ���
    pthread_attr_setinheritsched(&regattr, PTHREAD_EXPLICIT_SCHED);

//#if (defined PREEMPT) || (defined XENOMAI)
//    pthread_attr_setschedpolicy(&regattr, SCHED_OTHER);           //�����̵߳ĵ��Ȳ���
//#endif 

    pthread_attr_setschedparam(&regattr, &regparam);

    pthread_create(&tid, &regattr, __start_routine, 0) ;

    return tid;
}

void _funsyslog(int type, const char* str) {
    switch (type){
        case LOG_ERR:
            printf(RED"%s" NONE"\n", str);
            break;
        case LOG_WARN:
            printf(YELLOW"%s" NONE"\n", str);
            break;
        case LOG_INFO:
            printf(DARY_GRAY"%s" NONE"\n", str);
            break;
        default:
            printf("%s\n", str);
            break;
    }
}

void _fundbgPrint(const char* str) {
    printf("%s", str);
}




int main(int argc, char* argv[]) 
{
    SYS_BASE_TIME_uS = 500;
    SYS_BASE_TIME_S = (((double)SYS_BASE_TIME_uS) / 1000000);

   // printf("\nSYS_BASE_TIME_uS:%d  SYS_BASE_TIME_S:%f\n\n", SYS_BASE_TIME_uS, SYS_BASE_TIME_S);
    memset(pSysShareData,0,(uint64)sizeof(ShareData));

    pSysShareData->gSysCounter = 0;
    psErrInfo = &pSysShareData->sErrInfoPara;

    InitDriverPara();

    pSysShareData->sExSW.eSysState = Sys_State_PreloadPara;

    ErrorInfoArrayReset(&pSysShareData->sErrInfoPara);

    MainWorkLoop();

    memset(&pSysShareData->rt_ThreadExecInfo, 0, (uint64)sizeof(ThreadExecInfo));
    pSysShareData->rt_ThreadExecInfo.RunPeriod_uS = SYS_BASE_TIME_uS;

    memset(&pSysShareData->EtherCat_ThreadExecInfo, 0, (uint64)sizeof(ThreadExecInfo));
    pSysShareData->EtherCat_ThreadExecInfo.RunPeriod_uS = SYS_BASE_TIME_uS;

    pSysShareData->bNeedReSetCalibrat = false;
    pSysShareData->bNeedReSetChannel = false;
    pSysShareData->bActiveFieldbus = true;

    //���� �����������ߵ�����
    PushPointBuffAddr = (int64)&psChartsData->sChart[0].Points[0].T;
    pPushPointsCount = (int32*) & psChartsData->uPointsCount;
    pPushHadPushPoints = (int32*)&psChartsData->uHadPushPoints;

    pSysShareData->PushEoResultLen = 0;
    pPushEoBuff = &pSysShareData->pushEoResultBuffer[0];
    pushEoLen = &pSysShareData->PushEoResultLen;

    pSysShareData->pbConnected = &bExplorerConnectet;

    pthread_t tid_EtherCat,tid_rt, tid_fieldbus, tid_log ;
    pthread_t tid_func, tid_comm, tid_database, tid_fileServer, tid_fileClear;

    //����
    tid_log = StartThread("AUX", false, 2, LogThread);

    //���ݿ�           //DataBaseThread�в�Ҫ�������Ĺ���� SqliteApi�Ƕ�����;
    sqlite3_config(SQLITE_CONFIG_SERIALIZED);
    tid_database = StartThread("DataBase", false, 5, DataBaseThread);

    //ͨ���߳�
    tid_comm = StartThread("Comm_Server", false, 35, comm_thread);

    Sleep_mS(4000);     //�ȴ� DataBaseThread  ��ʼ�����  ������ʱ����� �������ݿ��������
    //ʵʱ�߳�
#if(SYSTEM_MOTION_MODE ==1)
    {
        tid_EtherCat = StartEtherCatThread(99, SYS_BASE_TIME_uS, EtherCatManager, &pSysShareData->EtherCat_ThreadExecInfo);
    }
#endif

    //����ˢ���߳�        EoҲ������ �ϲ�������
    tid_func = StartThread("function", false, 30, FunctionThread);

    //ʵʱ�߳�
#if (SYSTEM_MOTION_MODE == 1)
    {   
        tid_fieldbus = StartThread("fieldbus", false, 25, FieldbusThread);                    //����     

        tid_rt = StartThread("rtCalc", false, 40, MainRtThread);
    }
#else
    {
        //����        EoҲ������ �ϲ�������    
        tid_fieldbus = StartThread("fieldbus", false, 60, FieldbusThread);

        tid_rt = StartPressRtThread(99, (SYS_BASE_TIME_uS), RealtimeFunction, &pSysShareData->rt_ThreadExecInfo);
    }
#endif
    //�ļ��������߳�   
    tid_fileServer = StartThread("FileServer", false, 5, FileServer);                       //DataBaseThread�в�Ҫ�������Ĺ���� SqliteApi�Ƕ�����;

    //�ļ������߳�    SqliteClear()
    tid_fileClear = StartThread("FileClear", false, 6, FileClear);

    pthread_join(tid_log, NULL);
    pthread_join(tid_database, NULL);
    pthread_join(tid_fileServer, NULL);
    pthread_join(tid_fileClear, NULL);
    
    pthread_join(tid_fieldbus, NULL);
    pthread_join(tid_comm, NULL);
    pthread_join(tid_func, NULL);

#if (SYSTEM_MOTION_MODE == 1)
        pthread_join(tid_EtherCat, NULL);
#endif

    pthread_join(tid_rt, NULL);
    return NULL;
}


