#include "SeqItemMeasurement.h"
#include "rttimer.h"
#include "SysShareMemoryDefine.h"
#include "DynaVar.h"
SeqItem_MeasurementStart* pMeasurementStart;
SeqItem_MeasurementStop* pMeasurementStop;
/* SeqItem_MeasurementStart_Judge
* @param[in]     SeqItem* pSeqItem
* @return        ErrorInfo
*/
float32 xStart = 0;
float32 yStart = 0;

float32 xValueLastStop = 0;
float32 yValueLastStop = 0;
TrigData ResetTrig;

float32 xTrigVar, yTrigVar;
MeasureType pMeasureType;
ReturnPoint pReturnPoint;
SMeasure_Returntext pSMeasure_Returntext;
ErrorInfo SeqItem_MeasurementStart_Judge(bool* pbFirstJudgePoint, bool* pbManualFlag, bool* pbPlcIO, float32* pxValue, float32* pyValue, bool* pbStartRunSeq)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, (uint64)sizeof(ErrorInfo));

	if (pMeasurementStart != NULL)
	{
		pMeasureType.MeasureStart = pMeasurementStart->eMeasureMode;
		switch (pMeasurementStart->eMeasureMode)
		{
		case Auto:
			break;
		case ManualOp:
			if (*pbManualFlag && (pSysShareData->sExSW.eLastControlType == pSysShareData->sExSW.eControlType))
			{
				//printf("pbStartRunSeq  ManualOp \n\n");
				*pbStartRunSeq = true;
				yStart = *pyValue;
				xStart = *pxValue;
			}
			break;
		case PLC_IO:
			if (*pbPlcIO && (pSysShareData->sExSW.eLastControlType == pSysShareData->sExSW.eControlType))
			{
				yStart = *pyValue;
				xStart = *pxValue;
				*pbStartRunSeq = true;
				pMeasurementStart->StartContext.uMeasureContex.sPlcIo.bTrig = true;
			}
			break;
		case xTrigger:
		{
			if (*pbFirstJudgePoint)
			{
				xStart = *pxValue;
				*pbFirstJudgePoint = false;
				errorInfo = UpdateDynaVar(&pMeasurementStart->uMeasurePara.sxTriggerPara.dTriggerVar);
				//rt_dbgPrint(1, 2, "Init xStart:%f dTriggerVar:%f\n", xStart, pMeasurementStart->uMeasurePara.sxTriggerPara.dTriggerVar.fVarSwitched);
			}
			else
			{
				if (pMeasurementStart->uMeasurePara.sxTriggerPara.eDirection == LeftToRight)
				{
					if (xStart < pMeasurementStart->uMeasurePara.sxTriggerPara.dTriggerVar.fVarSwitched)
					{
						if (*pxValue > (pMeasurementStart->uMeasurePara.sxTriggerPara.dTriggerVar.fVarSwitched))
						{
							*pbStartRunSeq = true;
							pMeasurementStart->StartContext.uMeasureContex.sxTrigger.bTrig = true;
						}
					}
					else
					{
						ResetTrig.bTrigSignal = *pxValue < (pMeasurementStart->uMeasurePara.syTriggerPara.dTriggerVar.fVarSwitched - 0.005);//传感器的抖动系数
						R_Trig(&ResetTrig);
						if (ResetTrig.bTrig)
						{
							xStart = *pxValue;
							//rt_dbgPrint(1, 2, "Reset xStart:%f\n", xStart);
						}
					}
				}
				else if (pMeasurementStart->uMeasurePara.sxTriggerPara.eDirection == RightToLeft)
				{
					if (xStart > pMeasurementStart->uMeasurePara.sxTriggerPara.dTriggerVar.fVarSwitched)
					{
						if (*pxValue < (pMeasurementStart->uMeasurePara.sxTriggerPara.dTriggerVar.fVarSwitched))
						{
							*pbStartRunSeq = true;
							pMeasurementStart->StartContext.uMeasureContex.sxTrigger.bTrig = true;
						}
					}
					else
					{
						ResetTrig.bTrigSignal = *pxValue > (pMeasurementStart->uMeasurePara.syTriggerPara.dTriggerVar.fVarSwitched + 0.005);//传感器的抖动系数
						R_Trig(&ResetTrig);
						if (ResetTrig.bTrig)
						{
							xStart = *pxValue;
							rt_dbgPrint(1, 2, "Reset xStart:%f\n", xStart);
						}
					}
				}
			}
		}
		break;
		case yTrigger:
		{
			if (*pbFirstJudgePoint)
			{
				yStart = *pyValue;
				*pbFirstJudgePoint = false;
				//rt_dbgPrint(1, 2, "Init xStart:%f dTriggerVar:%f\n", yStart, pMeasurementStart->uMeasurePara.syTriggerPara.dTriggerVar.fVarSwitched);
				rt_dbgPrint(1, 2, "Init yStart:%f\n", yStart);
			}
			else
			{

				errorInfo = UpdateDynaVar(&pMeasurementStart->uMeasurePara.syTriggerPara.dTriggerVar);
				//if ((pMeasurementStart->uMeasurePara.syTriggerPara.dTriggerVar.fVarSwitched - pSysShareData->sSysFbkVar.fSenosrVarMin) < ((pSysShareData->sSysFbkVar.fSenosrVarMax - pSysShareData->sSysFbkVar.fSenosrVarMin) * 0.01))
				//	yTrigVar = (pSysShareData->sSysFbkVar.fSenosrVarMin + (pSysShareData->sSysFbkVar.fSenosrVarMax - pSysShareData->sSysFbkVar.fSenosrVarMin) * 0.01);
				//else
				yTrigVar = pMeasurementStart->uMeasurePara.syTriggerPara.dTriggerVar.fVarSwitched;

				if (pMeasurementStart->uMeasurePara.syTriggerPara.eDirection == DownToUp)
				{
					if (yStart < yTrigVar)//pMeasurementStart->uMeasurePara.syTriggerPara.dTriggerVar.fVarSwitched)
					{
						if (*pyValue > pMeasurementStart->uMeasurePara.syTriggerPara.dTriggerVar.fVarSwitched)		//进入判断逻辑的时候    实际上比较大
						{
							*pbStartRunSeq = true;
							pMeasurementStart->StartContext.uMeasureContex.syTrigger.bTrig = true;

							rt_dbgPrint(1, 2, "bTrig true  pyValue:%f > :%f \n", *pyValue, pMeasurementStart->uMeasurePara.syTriggerPara.dTriggerVar.fVarSwitched);
							printf("pMeasurementStart->uMeasurePara.syTriggerPara.dTriggerVar.fVarSwitched: %f\n", pMeasurementStart->uMeasurePara.syTriggerPara.dTriggerVar.fVarSwitched);
						}
					}
					else
					{
						ResetTrig.bTrigSignal = *pyValue < (yTrigVar - (abs(yTrigVar) * 0.01));// pMeasurementStart->uMeasurePara.syTriggerPara.dTriggerVar.fVarSwitched * 0.99;//传感器的抖动系数
						R_Trig(&ResetTrig);
						if (ResetTrig.bTrig)
						{
							yStart = *pyValue;
							rt_dbgPrint(1, 2, "Reset yStart:%f\n", yStart);
						}
					}
				}
				else if (pMeasurementStart->uMeasurePara.syTriggerPara.eDirection == UpToDown)
				{
					if (yStart > yTrigVar)//pMeasurementStart->uMeasurePara.syTriggerPara.dTriggerVar.fVarSwitched)
					{
						if (*pyValue < yTrigVar)//pMeasurementStart->uMeasurePara.syTriggerPara.dTriggerVar.fVarSwitched)		//进入判断逻辑的时候    实际上比较大
						{
							*pbStartRunSeq = true;
							pMeasurementStart->StartContext.uMeasureContex.syTrigger.bTrig = true;

							rt_dbgPrint(1, 2, "bTrig true  pyValue:%f > :%f \n", *pyValue, pMeasurementStart->uMeasurePara.syTriggerPara.dTriggerVar.fVarSwitched);
						}
					}
					else
					{
						ResetTrig.bTrigSignal = *pyValue > (yTrigVar + (abs(yTrigVar) * 0.01));// pMeasurementStart->uMeasurePara.syTriggerPara.dTriggerVar.fVarSwitched * 1.01;//传感器的抖动系数
						R_Trig(&ResetTrig);
						if (ResetTrig.bTrig)
						{
							yStart = *pyValue;
							rt_dbgPrint(1, 2, "Reset yStart:%f\n", yStart);
						}
					}
				}
			}
		}
		break;
		default:
			break;
		}
	}
	else
	{
		//errorInfo.ErrCode = 24340;
		//errorInfo.eErrLever = Warm;
	}
	if (pMeasurementStop != NULL)	//20250715：初始化折返点状态
	{
		pMeasureType.MeasureStop = pMeasurementStop->eMeasureMode;
		if (pMeasurementStop->eMeasureMode == CurveReturn)
		{
			pReturnPoint.ReturnState = true;
		}
		else
		{
			pReturnPoint.ReturnState = false;
			pReturnPoint.ReturnStop = false;
			pReturnPoint.ReturnIndex = 0;
			pReturnPoint.ReturnType = null;
			pSMeasure_Returntext.fXmax = 0;
			pSMeasure_Returntext.fXmin = 0;
			pSMeasure_Returntext.fYmax = 0;
			pSMeasure_Returntext.fYmin = 0;
		}
	}

	ErrorInfoPack(&errorInfo, "SeqItem_MeasurementStart_Judge", "");
	return errorInfo;
}


/* SeqItem_MeasurementStart_Init
* @param[in]     SeqItem* pSeqItem
* @return        ErrorInfo
*/
ErrorInfo SeqItem_MeasurementStart_Init(SeqItem* pSeqItem)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, (uint64)sizeof(ErrorInfo));

	SeqItem_MeasurementStart* pCfg = (SeqItem_MeasurementStart*)(pSeqItem->pCfg);
	pSeqItem->pSeqRef->bMeasurementStart = false;

	ErrorInfoPack(&errorInfo, "SeqItem_MeasurementStart_Init", "");
	return errorInfo;
}

/* SeqItem_MeasurementStart_Start
* @param[in]     SeqItem* pSeqItem
* @return        ErrorInfo
*/
ErrorInfo SeqItem_MeasurementStart_Start(SeqItem* pSeqItem)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, sizeof(ErrorInfo));

	SeqItem_MeasurementStart* pCfg = (SeqItem_MeasurementStart*)(pSeqItem->pCfg);
	MeasurementStart_Context* sStartContext = &pCfg->StartContext;

	pSeqItem->pSeqRef->bMeasurementStart = false;

	switch (pCfg->eMeasureMode)
	{
	case Auto:
		break;
	case ManualOp:
		break;
	case PLC_IO:
		break;
	case xTrigger:
		errorInfo = UpdateDynaVar(&pMeasurementStart->uMeasurePara.sxTriggerPara.dTriggerVar);
		//rt_dbgPrint(1, 2, "CurveReturn  dTriggerVar:%f\n", pMeasurementStart->uMeasurePara.sxTriggerPara.dTriggerVar.fVarSwitched);
		break;
	case yTrigger:
		errorInfo = UpdateDynaVar(&pMeasurementStart->uMeasurePara.syTriggerPara.dTriggerVar);
		break;
	case TimeMode:
		errorInfo = UpdateDynaVar(&pMeasurementStart->uMeasurePara.sfTimePara.dTime);
		break;
	case CurveReturn:
		break;
	default:
		break;
	}

	ErrorInfoPack(&errorInfo, "SeqItem_MeasurementStart_Start", "");
	return errorInfo;
}

/* SeqItem_MeasurementStart_Execute
* @param[in]     SeqItem* pSeqItem
* @return        ErrorInfo
*/
ErrorInfo SeqItem_MeasurementStart_Execute(SeqItem* pSeqItem)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, (uint64)sizeof(ErrorInfo));

	SeqItem_MeasurementStart* pCfg = (SeqItem_MeasurementStart*)(pSeqItem->pCfg);
	MeasurementStart_Context* sStartContext = &pCfg->StartContext;

	pSeqItem->pExeActor->bBusy = true;
	//rt_dbgPrint(1, 2, "MeasurementStart Execute\n");
	switch (pCfg->eMeasureMode)
	{
	case Auto:
		pSeqItem->pSeqRef->bMeasurementStart = true;
		break;
	case ManualOp:
		pSeqItem->pSeqRef->bMeasurementStart = true;
		break;
	case PLC_IO:
		if (sStartContext->uMeasureContex.sPlcIo.bTrig)
		{
			pSeqItem->pSeqRef->bMeasurementStart = true;
			sStartContext->uMeasureContex.sPlcIo.bTrig = false;
		}
		break;
	case xTrigger:
		if (sStartContext->uMeasureContex.sxTrigger.bTrig)
		{
			pSeqItem->pSeqRef->bMeasurementStart = true;
			sStartContext->uMeasureContex.sxTrigger.bTrig = false;
		}
		break;
	case yTrigger:
		if (sStartContext->uMeasureContex.syTrigger.bTrig)
		{
			sStartContext->uMeasureContex.syTrigger.bTrig = false;
			pSeqItem->pSeqRef->bMeasurementStart = true;
		}
		break;
	default:
		break;
	}

	if (pSeqItem->pSeqRef->bMeasurementStart)
	{
		SeqExeItemExit(pSeqItem->pExeActor, errorInfo);

	}

	ErrorInfoPack(&errorInfo, "SeqItem_MeasurementStart_Execute", "");
	return errorInfo;
}


/* SeqItem_MeasurementStop_Init
* @param[in]     SeqItem* pSeqItem
* @return        ErrorInfo
*/
ErrorInfo SeqItem_MeasurementStop_Init(SeqItem* pSeqItem)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, (uint64)sizeof(ErrorInfo));
	pReturnPoint.ReturnStop = false;

	//SeqItem_MeasurementStop* pCfg = (SeqItem_MeasurementStop*)(pSeqItem->pCfg);

	ErrorInfoPack(&errorInfo, "SeqItem_MeasurementStop_Init", "");
	return errorInfo;
}

/* SeqItem_MeasurementStop_Start
* @param[in]     SeqItem* pSeqItem
* @return        ErrorInfo
*/
ErrorInfo SeqItem_MeasurementStop_Start(SeqItem* pSeqItem)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, (uint64)sizeof(ErrorInfo));
	SeqItem_MeasurementStop* pCfg = (SeqItem_MeasurementStop*)(pSeqItem->pCfg);
	pReturnPoint.ReturnValue = &pCfg->StopContext.uMeasureContex.sReturn;
	MeasurementStop_Context* sStopContext = &pCfg->StopContext;

	switch (pCfg->eMeasureMode)
	{
	case Auto:
		break;
	case ManualOp:
		sStopContext->uMeasureContex.sManualOp.bTrig = false;
		break;
	case PLC_IO:
		sStopContext->uMeasureContex.sPlcIo.bTrig = false;
		break;
	case xTrigger:
		sStopContext->uMeasureContex.sxTrigger.bHadInitTime = false;
		errorInfo = UpdateDynaVar(&pCfg->uMeasurePara.sxTriggerPara.dTriggerVar);

		if (!errorInfo.ErrCode)
			errorInfo = UpdateDynaVar(&pCfg->uMeasurePara.sxTriggerPara.dTimeOut);

		break;
	case yTrigger:
		sStopContext->uMeasureContex.syTrigger.bHadInitTime = false;
		errorInfo = UpdateDynaVar(&pCfg->uMeasurePara.syTriggerPara.dTriggerVar);
		if (!errorInfo.ErrCode)
			errorInfo = UpdateDynaVar(&pCfg->uMeasurePara.syTriggerPara.dTimeOut);

		break;
	case TimeMode:
		sStopContext->uMeasureContex.sfTime.bHadInitTime = false;
		errorInfo = UpdateDynaVar(&pCfg->uMeasurePara.sfTimePara.dTime);
		if (pCfg->uMeasurePara.sfTimePara.dTime.fVarSwitched < 0)		//结束等待时间为负数时，报错
		{
			errorInfo.ErrCode = 24342;
			errorInfo.eErrLever = Error;
			pCfg->uMeasurePara.sfTimePara.dTime.fVarSwitched = 0;
		}
		break;
	case CurveReturn:
		pCfg->StopContext.uMeasureContex.sReturn.fXLast = pSysShareData->sSysFbkVar.fExtPosFbk;
		pCfg->StopContext.uMeasureContex.sReturn.fXmax = pSysShareData->sSysFbkVar.fExtPosFbk;

		pCfg->StopContext.uMeasureContex.sReturn.fYLast = pSysShareData->sSysFbkVar.fSenosrVarFbk;
		pCfg->StopContext.uMeasureContex.sReturn.fYmax = pSysShareData->sSysFbkVar.fSenosrVarFbk;

		pCfg->StopContext.uMeasureContex.sReturn.fXmin = pSysShareData->sSysFbkVar.fExtPosFbk;
		pCfg->StopContext.uMeasureContex.sReturn.fYmin = pSysShareData->sSysFbkVar.fSenosrVarFbk;
		//rt_dbgPrint(1, 2, "CurveReturn Init fXLast:%f  fXmax:%f\n",
		//	pCfg->StopContext.uMeasureContex.sReturn.fXLast,
		//	pCfg->StopContext.uMeasureContex.sReturn.fXmax);

		//设置折返点模式
		pReturnPoint.ReturnState = 1;
		switch (pCfg->uMeasurePara.sReturnPara.eReturnType)
		{
		case Xmin:
			pReturnPoint.ReturnType = Xmin;
			break;
		case Xmax:
			pReturnPoint.ReturnType = Xmax;
			break;
		case Ymin:
			pReturnPoint.ReturnType = Ymin;
			break;
		case Ymax:
			pReturnPoint.ReturnType = Ymax;
			break;
		}
		break;
	default:
		break;
	}

	ErrorInfoPack(&errorInfo, "SeqItem_MeasurementStop_Start", "");
	return errorInfo;
}


/** AppendChannelDataToBuffer
* MeasureStop判断折返点逻辑
* @param[in]    pReturnPoint：折返点数据
* @param[in]    Returntext：工艺折返记录数据
* @param[in]    pSeqItem：工艺参数
* @param[out]   None
* @return       None
*/
void ReturnJudge(ReturnPoint* pReturnPoint, SMeasure_Returntext* Returntext, SeqItem* pSeqItem)
{
	switch (psChartsData->eSampleType)
	{
	case Interval_Time_4000:			//超采样处理 使用采样后数据判断
	case Interval_Time_2000:
	{
		if (pReturnPoint->ReturnType == Xmax)
		{
			if ((psChartsData->sChart[0].Points[psChartsData->uPointsCount - 1].X > Returntext->fXmax) && (psChartsData->uPointsCount > 1))		//更新最大值
			{
				Returntext->fXmax = psChartsData->sChart[0].Points[psChartsData->uPointsCount - 1].X;
				pReturnPoint->ReturnIndex = psChartsData->uPointsCount - 1;
			}
			if (psChartsData->sChart[0].Points[psChartsData->uPointsCount - 1].X < (Returntext->fXmax - 0.008))
			{
				printf("\n\nMeasureStop\n");
				pSeqItem->pSeqRef->bMeasurementStart = false;
				rt_dbgPrint(1, 2, "bMeasurementStart false\n");
				printf("pReturnPoint.ReturnValue: %f\n", pReturnPoint->ReturnValue->fXmax);
				printf("pCfg->StopContext.uMeasureContex.sReturn.fXmax: %f\n", Returntext->fXmax);
			}
			Returntext->fXLast = psChartsData->sChart[0].Points[psChartsData->uPointsCount - 1].X;
			break;
		}
		else if (pReturnPoint->ReturnType == Ymax)
		{
			if ((psChartsData->sChart[0].Points[psChartsData->uPointsCount - 1].Y > Returntext->fYmax) && (psChartsData->uPointsCount > 1))		//更新最大值
			{
				Returntext->fYmax = psChartsData->sChart[0].Points[psChartsData->uPointsCount - 1].Y;
				pReturnPoint->ReturnIndex = psChartsData->uPointsCount - 1;
			}
			if (psChartsData->sChart[0].Points[psChartsData->uPointsCount - 1].Y < (Returntext->fYmax - 0.008))
			{
				printf("\n\nMeasureStop\n");
				pSeqItem->pSeqRef->bMeasurementStart = false;
				rt_dbgPrint(1, 2, "bMeasurementStart false\n");
				printf("pReturnPoint.ReturnValue: %f\n", pReturnPoint->ReturnValue->fYmax);
				printf("pCfg->StopContext.uMeasureContex.sReturn.fYmax: %f\n", Returntext->fYmax);
			}
			Returntext->fYLast = psChartsData->sChart[0].Points[psChartsData->uPointsCount - 1].Y;
			break;
		}
		else if (pReturnPoint->ReturnType == Xmin)
		{
			if ((psChartsData->sChart[0].Points[psChartsData->uPointsCount - 1].X < Returntext->fXmin) && (psChartsData->uPointsCount > 1))		//更新最大值
			{
				Returntext->fXmin = psChartsData->sChart[0].Points[psChartsData->uPointsCount - 1].X;
				pReturnPoint->ReturnIndex = psChartsData->uPointsCount - 1;
			}
			if (psChartsData->sChart[0].Points[psChartsData->uPointsCount - 1].X > (Returntext->fXmin - 0.008))
			{
				printf("\n\nMeasureStop\n");
				pSeqItem->pSeqRef->bMeasurementStart = false;
				rt_dbgPrint(1, 2, "bMeasurementStart false\n");
				printf("pReturnPoint.ReturnValue: %f\n", pReturnPoint->ReturnValue->fXmin);
				printf("pCfg->StopContext.uMeasureContex.sReturn.fXmin: %f\n", Returntext->fXmin);
			}
			Returntext->fXLast = psChartsData->sChart[0].Points[psChartsData->uPointsCount - 1].X;
			break;
		}
		else if (pReturnPoint->ReturnType == Ymin)
		{
			if ((psChartsData->sChart[0].Points[psChartsData->uPointsCount - 1].Y < Returntext->fYmin) && (psChartsData->uPointsCount > 1))		//更新最大值
			{
				Returntext->fYmin = psChartsData->sChart[0].Points[psChartsData->uPointsCount - 1].Y;
				pReturnPoint->ReturnIndex = psChartsData->uPointsCount - 1;
			}
			if (psChartsData->sChart[0].Points[psChartsData->uPointsCount - 1].Y > (Returntext->fYmin - 0.008))
			{
				printf("\n\nMeasureStop\n");
				pSeqItem->pSeqRef->bMeasurementStart = false;
				rt_dbgPrint(1, 2, "bMeasurementStart false\n");
				printf("pReturnPoint.ReturnValue: %f\n", pReturnPoint->ReturnValue->fYmin);
				printf("pCfg->StopContext.uMeasureContex.sReturn.fYmin: %f\n", Returntext->fYmin);
			}
			Returntext->fYLast = psChartsData->sChart[0].Points[psChartsData->uPointsCount - 1].Y;
			break;
		}
	}
	// 采用采样前数据判断
	case Interval_Time_1000:		//间隔周期执行
	case Interval_Time_0500:
	case Interval_Time_0100:
	case Interval_Time_0050:
	case Interval_Time_0010:
	case Interval_Pos_1000um:		//1mm位置等间隔采样
	case Interval_Pos_0100um:		//0.1mm位置等间隔采样
	case Interval_Pos_0010um:		//0.01mm位置等间隔采样
	case Interval_Force_1000N:		//1KN压力等间隔采样
	case Interval_Force_0100N:		//0.1KN压力等间隔采样
	case Interval_Force_0010N:		//0.01KN压力等间隔采样
	{
		if (pReturnPoint->ReturnType == Xmax)
		{
			if ((pSysShareData->sSysFbkVar.fExtPosFbk > Returntext->fXmax) && (pSysShareData->sSysFbkVar.fExtPosFbk > pReturnPoint->ReturnValue->fXmax))		//更新最大值
				Returntext->fXmax = pSysShareData->sSysFbkVar.fExtPosFbk;
			//if((pSysShareData->sSysFbkVar.fExtPosFbk < pCfg->StopContext.uMeasureContex.sReturn.fXmax) &&	//当前值小于最大值 而且上一个值>= 最大值  判定已经return了。
			//	(pCfg->StopContext.uMeasureContex.sReturn.fXLast >= pCfg->StopContext.uMeasureContex.sReturn.fXmax))
			if (pSysShareData->sSysFbkVar.fExtPosFbk < (Returntext->fXmax - 0.008))	//
			{
				printf("\n\nMeasureStop\n");
				pSeqItem->pSeqRef->bMeasurementStart = false;
				rt_dbgPrint(1, 2, "bMeasurementStart false\n");
				//printf("MeasureStop-pReturnPoint.ReturnIndex: %d\n", pReturnPoint.ReturnIndex);
				printf("pReturnPoint.ReturnValue: %f\n", pReturnPoint->ReturnValue->fXmax);
				printf("pCfg->StopContext.uMeasureContex.sReturn.fXmax: %f\n", Returntext->fXmax);
			}
			Returntext->fXLast = pSysShareData->sSysFbkVar.fExtPosFbk;
			break;
		}
		else if (pReturnPoint->ReturnType == Ymax)
		{
			if (pSysShareData->sSysFbkVar.fSenosrVarFbk > Returntext->fYmax)		//更新最大值
				Returntext->fYmax = pSysShareData->sSysFbkVar.fSenosrVarFbk;

			//if ((pSysShareData->sSysFbkVar.fSenosrVarFbk < pCfg->StopContext.uMeasureContex.sReturn.fYmax) &&	//当前值小于最大值 而且上一个值>= 最大值  判定已经return了。
			//	(pCfg->StopContext.uMeasureContex.sReturn.fYLast >= pCfg->StopContext.uMeasureContex.sReturn.fYmax))
			if (pSysShareData->sSysFbkVar.fSenosrVarFbk < (Returntext->fYmax - pSysShareData->sSysFbkVar.fSenosrVarMax * 0.01))	//当前值小于最大值 而且上一个值>= 最大值  判定已经return了。
			{
				printf("\n\nMeasureStop\n");
				pSeqItem->pSeqRef->bMeasurementStart = false;
				rt_dbgPrint(1, 2, "fSenosrVarFbk:%f  fReturn:%f\n",
					pSysShareData->sSysFbkVar.fSenosrVarFbk,
					(Returntext->fYmax - pSysShareData->sSysFbkVar.fSenosrVarMax * 0.01));
				printf("pReturnPoint.ReturnValue: %f\n", pReturnPoint->ReturnValue->fYmax);
				printf("pCfg->StopContext.uMeasureContex.sReturn.fYmax: %f\n", Returntext->fYmax);
			}
			Returntext->fYLast = pSysShareData->sSysFbkVar.fSenosrVarFbk;
			break;
		}
		else if (pReturnPoint->ReturnType == Xmin)
		{
			if ((pSysShareData->sSysFbkVar.fExtPosFbk < Returntext->fXmin) && (pSysShareData->sSysFbkVar.fExtPosFbk < pReturnPoint->ReturnValue->fXmin))		//更新最大值
				Returntext->fXmin = pSysShareData->sSysFbkVar.fExtPosFbk;
			if (pSysShareData->sSysFbkVar.fExtPosFbk > (Returntext->fXmin - 0.008))	//
			{
				printf("\n\nMeasureStop\n");
				pSeqItem->pSeqRef->bMeasurementStart = false;
				rt_dbgPrint(1, 2, "bMeasurementStart false\n");
				//printf("MeasureStop-pReturnPoint.ReturnIndex: %d\n", pReturnPoint.ReturnIndex);
				printf("pReturnPoint.ReturnValue: %f\n", pReturnPoint->ReturnValue->fXmin);
				printf("pCfg->StopContext.uMeasureContex.sReturn.fXmin: %f\n", Returntext->fXmin);
			}
			Returntext->fXLast = pSysShareData->sSysFbkVar.fExtPosFbk;
			break;
		}
		else if (pReturnPoint->ReturnType == Ymin)
		{
			if (pSysShareData->sSysFbkVar.fSenosrVarFbk < Returntext->fYmin)		//更新最大值
				Returntext->fYmin = pSysShareData->sSysFbkVar.fSenosrVarFbk;

			if (pSysShareData->sSysFbkVar.fSenosrVarFbk > (Returntext->fYmin - pSysShareData->sSysFbkVar.fSenosrVarMax * 0.01))	//当前值小于最大值 而且上一个值>= 最大值  判定已经return了。
			{
				printf("\n\nMeasureStop\n");
				pSeqItem->pSeqRef->bMeasurementStart = false;
				rt_dbgPrint(1, 2, "fSenosrVarFbk:%f  fReturn:%f\n",
					pSysShareData->sSysFbkVar.fSenosrVarFbk,
					(Returntext->fYmin - pSysShareData->sSysFbkVar.fSenosrVarMax * 0.01));
				printf("pReturnPoint.ReturnValue: %f\n", pReturnPoint->ReturnValue->fYmin);
				printf("pCfg->StopContext.uMeasureContex.sReturn.fYmin: %f\n", Returntext->fYmin);
			}
			Returntext->fYLast = pSysShareData->sSysFbkVar.fSenosrVarFbk;
			break;
			break;
		}
	}
	}
}

/* SeqItem_MeasurementStop_Execute
* @param[in]     SeqItem* pSeqItem
* @return        ErrorInfo
*/
ErrorInfo SeqItem_MeasurementStop_Execute(SeqItem* pSeqItem)
{
	ErrorInfo errorInfo;
	memset(&errorInfo, 0, (uint64)sizeof(ErrorInfo));

	SeqItem_MeasurementStop* pCfg = (SeqItem_MeasurementStop*)(pSeqItem->pCfg);

	//rt_dbgPrint(1, 2, "MeasurementStop Execute bMeasurementStart:%s\n", pSeqItem->pSeqRef->bMeasurementStart?"true":"false");
	MeasurementStop_Context* sStopContext = &pCfg->StopContext;
	pSeqItem->pExeActor->bBusy = true;
	//rt_dbgPrint(1, 2, "MeasurementStart Execute\n");
	switch (pCfg->eMeasureMode)
	{
	case Auto:
		if (pSeqItem->pSeqRef->bMeasurementStart)
			pSeqItem->pSeqRef->bMeasurementStart = false;
		break;
	case ManualOp:
		if (pSeqItem->pSeqRef->bMeasurementStart)
		{
			if (pSeqItem->pSeqRef->bStopRequest)//sStopContext->uMeasureContex.sManualOp.bTrig)
			{
				pSeqItem->pSeqRef->bMeasurementStart = false;
			}
		}
		break;
	case PLC_IO:
		if (pSeqItem->pSeqRef->bMeasurementStart)
		{
			if (sStopContext->uMeasureContex.sPlcIo.bTrig)
			{
				pSeqItem->pSeqRef->bMeasurementStart = false;
			}
		}
		break;
	case xTrigger:
		if (pSeqItem->pSeqRef->bMeasurementStart)
		{
			if (!sStopContext->uMeasureContex.sxTrigger.bHadInitTime)
			{
				sStopContext->uMeasureContex.sxTrigger.bHadInitTime = true;
				clock_gettime_nS(&sStopContext->uMeasureContex.sxTrigger.initTimestamp);
				xValueLastStop = pSysShareData->sSysFbkVar.fExtPosFbk;	//重置第一次进来的值
			}
			else
			{
				clock_gettime_nS(&sStopContext->uMeasureContex.sxTrigger.currTimestamp);
				if ((sStopContext->uMeasureContex.sxTrigger.currTimestamp - sStopContext->uMeasureContex.sxTrigger.initTimestamp)
					>= ((uint64)(pCfg->uMeasurePara.sxTriggerPara.dTimeOut.fVarSwitched * 1000000000.0)))
				{
					errorInfo.ErrCode = 24406;
					errorInfo.eErrLever = Error;
				}
			}

			if (!errorInfo.ErrCode)
			{
				if (pSeqItem->pSeqRef->bStopRequest)			//允许手动模式结束
				{
					pSeqItem->pSeqRef->bMeasurementStart = false;
				}
				else
				{
					if (pCfg->uMeasurePara.sxTriggerPara.eDirection == LeftToRight)
					{
						if ((pSysShareData->sSysFbkVar.fExtPosFbk > pCfg->uMeasurePara.sxTriggerPara.dTriggerVar.fVarSwitched) &&
							(xValueLastStop < pCfg->uMeasurePara.sxTriggerPara.dTriggerVar.fVarSwitched))
						{
							pSeqItem->pSeqRef->bMeasurementStart = false;
						}
					}
					else if (pCfg->uMeasurePara.sxTriggerPara.eDirection == RightToLeft)
					{
						if ((pSysShareData->sSysFbkVar.fExtPosFbk < pCfg->uMeasurePara.sxTriggerPara.dTriggerVar.fVarSwitched) &&
							(xValueLastStop > pCfg->uMeasurePara.sxTriggerPara.dTriggerVar.fVarSwitched))
						{
							pSeqItem->pSeqRef->bMeasurementStart = false;
						}
					}
					xValueLastStop = pSysShareData->sSysFbkVar.fExtPosFbk;
				}
			}
			else
			{
				pSeqItem->pSeqRef->bMeasurementStart = false;
			}
		}

		break;
	case yTrigger:
		if (pSeqItem->pSeqRef->bMeasurementStart)
		{
			rt_dbgPrint(1, 2, "fSenosrVarFbk:%f  fVarSwitched:%f yValueLastStop:%f\n",
				pSysShareData->sSysFbkVar.fSenosrVarFbk,
				pCfg->uMeasurePara.syTriggerPara.dTriggerVar.fVarSwitched,
				yValueLastStop);

			if (!sStopContext->uMeasureContex.syTrigger.bHadInitTime)
			{
				sStopContext->uMeasureContex.syTrigger.bHadInitTime = true;
				clock_gettime_nS(&sStopContext->uMeasureContex.syTrigger.initTimestamp);
				yValueLastStop = pSysShareData->sSysFbkVar.fSenosrVarFbk;	//重置第一次进来的值
			}
			else
			{
				clock_gettime_nS(&sStopContext->uMeasureContex.syTrigger.currTimestamp);
				if ((sStopContext->uMeasureContex.syTrigger.currTimestamp - sStopContext->uMeasureContex.syTrigger.initTimestamp)
					>= ((uint64)(pCfg->uMeasurePara.syTriggerPara.dTimeOut.fVarSwitched * 1000000000.0)))
				{
					errorInfo.ErrCode = 24407;
					errorInfo.eErrLever = Error;
				}
			}

			if (!errorInfo.ErrCode)
			{
				if (pSeqItem->pSeqRef->bStopRequest)			//允许手动模式结束
				{
					pSeqItem->pSeqRef->bMeasurementStart = false;
				}
				else
				{
					//if (pCfg->uMeasurePara.syTriggerPara.dTriggerVar.fVarSwitched < pSysShareData->sSysFbkVar.fSenosrVarMax * 0.01)
					//{
					//	if (pCfg->uMeasurePara.syTriggerPara.eDirection == UpToDown)
					//	{
					//		if (pSysShareData->sSysFbkVar.fSenosrVarFbk < pCfg->uMeasurePara.syTriggerPara.dTriggerVar.fVarSwitched)
					//			pSeqItem->pSeqRef->bMeasurementStart = false;
					//	}
					//	else
					//	{
					//		if (pSysShareData->sSysFbkVar.fSenosrVarFbk > pCfg->uMeasurePara.syTriggerPara.dTriggerVar.fVarSwitched)
					//			pSeqItem->pSeqRef->bMeasurementStart = false;
					//	}
					//}
					//else
					//{
					if (pCfg->uMeasurePara.syTriggerPara.eDirection == UpToDown)
					{
						if (pSysShareData->sSysFbkVar.fSenosrVarFbk < pCfg->uMeasurePara.syTriggerPara.dTriggerVar.fVarSwitched &&
							(yValueLastStop > pCfg->uMeasurePara.syTriggerPara.dTriggerVar.fVarSwitched))
						{
							pSeqItem->pSeqRef->bMeasurementStart = false;
						}
					}
					else if (pCfg->uMeasurePara.syTriggerPara.eDirection == DownToUp)
					{
						if (pSysShareData->sSysFbkVar.fSenosrVarFbk > pCfg->uMeasurePara.syTriggerPara.dTriggerVar.fVarSwitched &&
							(yValueLastStop < pCfg->uMeasurePara.syTriggerPara.dTriggerVar.fVarSwitched))
						{
							pSeqItem->pSeqRef->bMeasurementStart = false;
						}
					}
					yValueLastStop = pSysShareData->sSysFbkVar.fSenosrVarFbk;
					//}

				}
			}
			else
			{
				pSeqItem->pSeqRef->bMeasurementStart = false;
			}
		}
		break;
	case TimeMode:
		if (pSeqItem->pSeqRef->bMeasurementStart)
		{
			if (!sStopContext->uMeasureContex.sfTime.bHadInitTime)
			{
				sStopContext->uMeasureContex.sfTime.bHadInitTime = true;
				clock_gettime_nS(&sStopContext->uMeasureContex.sfTime.initTimestamp);
			}
			else
			{
				clock_gettime_nS(&sStopContext->uMeasureContex.sfTime.currTimestamp);
				if ((sStopContext->uMeasureContex.sfTime.currTimestamp - sStopContext->uMeasureContex.sfTime.initTimestamp)
					>= ((uint64)(pCfg->uMeasurePara.sfTimePara.dTime.fVarSwitched * 1000000000.0)))
				{
					pSeqItem->pSeqRef->bMeasurementStart = false;
				}
			}
		}
		break;
	case CurveReturn:
		if (pSeqItem->pSeqRef->bMeasurementStart)
		{
			if (pCfg->uMeasurePara.sReturnPara.eReturnMode == Left_Reutrn)
			{
				//ReturnJudge(&pReturnPoint, &pCfg->StopContext.uMeasureContex.sReturn, pSeqItem);
				if (pCfg->uMeasurePara.sReturnPara.eReturnType == Xmax)
				{
					if (pSysShareData->sSysFbkVar.fExtPosFbk > pCfg->StopContext.uMeasureContex.sReturn.fXmax)		//更新最大值
						pCfg->StopContext.uMeasureContex.sReturn.fXmax = pSysShareData->sSysFbkVar.fExtPosFbk;
					//if((pSysShareData->sSysFbkVar.fExtPosFbk < pCfg->StopContext.uMeasureContex.sReturn.fXmax) &&	//当前值小于最大值 而且上一个值>= 最大值  判定已经return了。
					//	(pCfg->StopContext.uMeasureContex.sReturn.fXLast >= pCfg->StopContext.uMeasureContex.sReturn.fXmax))
					if (pSysShareData->sSysFbkVar.fExtPosFbk < (pCfg->StopContext.uMeasureContex.sReturn.fXmax - 0.008))	//
					{
						pSeqItem->pSeqRef->bMeasurementStart = false;
						rt_dbgPrint(1, 2, "bMeasurementStart false\n");
					}
					//if(pSysShareData->gSysCounter % 2000)
					//	rt_dbgPrint(1, 2, "CurveReturn Append fExtPosFbk:%f  fXLast:%f fXmax:%f\n",
					//		pSysShareData->sSysFbkVar.fExtPosFbk,
					//		pCfg->StopContext.uMeasureContex.sReturn.fXLast,
					//		pCfg->StopContext.uMeasureContex.sReturn.fXmax);
					pCfg->StopContext.uMeasureContex.sReturn.fXLast = pSysShareData->sSysFbkVar.fExtPosFbk;
				}
				else if (pCfg->uMeasurePara.sReturnPara.eReturnType == Ymax)
				{
					if (pSysShareData->sSysFbkVar.fSenosrVarFbk > pCfg->StopContext.uMeasureContex.sReturn.fYmax)		//更新最大值
						pCfg->StopContext.uMeasureContex.sReturn.fYmax = pSysShareData->sSysFbkVar.fSenosrVarFbk;

					//if ((pSysShareData->sSysFbkVar.fSenosrVarFbk < pCfg->StopContext.uMeasureContex.sReturn.fYmax) &&	//当前值小于最大值 而且上一个值>= 最大值  判定已经return了。
					//	(pCfg->StopContext.uMeasureContex.sReturn.fYLast >= pCfg->StopContext.uMeasureContex.sReturn.fYmax))
					if (pSysShareData->sSysFbkVar.fSenosrVarFbk < (pCfg->StopContext.uMeasureContex.sReturn.fYmax - pSysShareData->sSysFbkVar.fSenosrVarMax * 0.01))	//当前值小于最大值 而且上一个值>= 最大值  判定已经return了。
					{
						pSeqItem->pSeqRef->bMeasurementStart = false;
						rt_dbgPrint(1, 2, "fSenosrVarFbk:%f  fReturn:%f\n",
							pSysShareData->sSysFbkVar.fSenosrVarFbk,
							(pCfg->StopContext.uMeasureContex.sReturn.fYmax - pSysShareData->sSysFbkVar.fSenosrVarMax * 0.01));
					}
					pCfg->StopContext.uMeasureContex.sReturn.fYLast = pSysShareData->sSysFbkVar.fSenosrVarFbk;
				}
			}
			else if (pCfg->uMeasurePara.sReturnPara.eReturnMode == Right_Return)
			{
				//ReturnJudge(&pReturnPoint, &pCfg->StopContext.uMeasureContex.sReturn, pSeqItem);
				if (pCfg->uMeasurePara.sReturnPara.eReturnType == Xmin)
				{
					if (pSysShareData->sSysFbkVar.fExtPosFbk < pCfg->StopContext.uMeasureContex.sReturn.fXmin)		//更新最小值
						pCfg->StopContext.uMeasureContex.sReturn.fXmin = pSysShareData->sSysFbkVar.fExtPosFbk;

					//if ((pSysShareData->sSysFbkVar.fExtPosFbk > pCfg->StopContext.uMeasureContex.sReturn.fXmin) &&	//当前值小于最大值 而且上一个值>= 最大值  判定已经return了。
					//	(pCfg->StopContext.uMeasureContex.sReturn.fXLast <= pCfg->StopContext.uMeasureContex.sReturn.fXmin))
					if (pSysShareData->sSysFbkVar.fExtPosFbk > (pCfg->StopContext.uMeasureContex.sReturn.fXmin + 0.008))	//
					{
						pSeqItem->pSeqRef->bMeasurementStart = false;
					}
					pCfg->StopContext.uMeasureContex.sReturn.fXLast = pSysShareData->sSysFbkVar.fExtPosFbk;
				}
				else if (pCfg->uMeasurePara.sReturnPara.eReturnType == Ymin)
				{
					if (pSysShareData->sSysFbkVar.fSenosrVarFbk < pCfg->StopContext.uMeasureContex.sReturn.fYmin)		//更新最小值
						pCfg->StopContext.uMeasureContex.sReturn.fYmin = pSysShareData->sSysFbkVar.fSenosrVarFbk;

					//if ((pSysShareData->sSysFbkVar.fSenosrVarFbk > pCfg->StopContext.uMeasureContex.sReturn.fYmin) &&	//当前值小于最大值 而且上一个值>= 最大值  判定已经return了。
					//	(pCfg->StopContext.uMeasureContex.sReturn.fYLast <= pCfg->StopContext.uMeasureContex.sReturn.fYmin))
					if ((pSysShareData->sSysFbkVar.fSenosrVarFbk > pCfg->StopContext.uMeasureContex.sReturn.fYmin * 1.008))	//当前值小于最大值 而且上一个值>= 最大值  判定已经return了。
					{
						pSeqItem->pSeqRef->bMeasurementStart = false;
					}
					pCfg->StopContext.uMeasureContex.sReturn.fYLast = pSysShareData->sSysFbkVar.fSenosrVarFbk;
				}
				else if (pCfg->uMeasurePara.sReturnPara.eReturnType == Ymax)
				{
					if (pSysShareData->sSysFbkVar.fSenosrVarFbk > pCfg->StopContext.uMeasureContex.sReturn.fYmax)		//更新最大值
						pCfg->StopContext.uMeasureContex.sReturn.fYmax = pSysShareData->sSysFbkVar.fSenosrVarFbk;

					//if ((pSysShareData->sSysFbkVar.fSenosrVarFbk < pCfg->StopContext.uMeasureContex.sReturn.fYmax) &&	//当前值小于最大值 而且上一个值>= 最大值  判定已经return了。
					//	(pCfg->StopContext.uMeasureContex.sReturn.fYLast >= pCfg->StopContext.uMeasureContex.sReturn.fYmax))
					if (pSysShareData->sSysFbkVar.fSenosrVarFbk < (pCfg->StopContext.uMeasureContex.sReturn.fYmax - pSysShareData->sSysFbkVar.fSenosrVarMax * 0.01))	//当前值小于最大值 而且上一个值>= 最大值  判定已经return了。
					{
						pSeqItem->pSeqRef->bMeasurementStart = false;
						rt_dbgPrint(1, 2, "fSenosrVarFbk:%f  fReturn:%f\n",
							pSysShareData->sSysFbkVar.fSenosrVarFbk,
							(pCfg->StopContext.uMeasureContex.sReturn.fYmax - pSysShareData->sSysFbkVar.fSenosrVarMax * 0.01));
					}

					pCfg->StopContext.uMeasureContex.sReturn.fYLast = pSysShareData->sSysFbkVar.fSenosrVarFbk;
				}
				else if (pCfg->uMeasurePara.sReturnPara.eReturnType == Xmax)
				{
					if (pSysShareData->sSysFbkVar.fExtPosFbk > pCfg->StopContext.uMeasureContex.sReturn.fXmax)		//更新最大值
						pCfg->StopContext.uMeasureContex.sReturn.fXmax = pSysShareData->sSysFbkVar.fExtPosFbk;
					//if((pSysShareData->sSysFbkVar.fExtPosFbk < pCfg->StopContext.uMeasureContex.sReturn.fXmax) &&	//当前值小于最大值 而且上一个值>= 最大值  判定已经return了。
					//	(pCfg->StopContext.uMeasureContex.sReturn.fXLast >= pCfg->StopContext.uMeasureContex.sReturn.fXmax))
					if (pSysShareData->sSysFbkVar.fExtPosFbk < (pCfg->StopContext.uMeasureContex.sReturn.fXmax - 0.008))	//
					{
						pSeqItem->pSeqRef->bMeasurementStart = false;
						rt_dbgPrint(1, 2, "bMeasurementStart false\n");
					}
					//if(pSysShareData->gSysCounter % 2000)
					//	rt_dbgPrint(1, 2, "CurveReturn Append fExtPosFbk:%f  fXLast:%f fXmax:%f\n",
					//		pSysShareData->sSysFbkVar.fExtPosFbk,
					//		pCfg->StopContext.uMeasureContex.sReturn.fXLast,
					//		pCfg->StopContext.uMeasureContex.sReturn.fXmax);
					pCfg->StopContext.uMeasureContex.sReturn.fXLast = pSysShareData->sSysFbkVar.fExtPosFbk;
				}
			}
		}
		break;
	default:
		break;
	}

	if (!pSeqItem->pSeqRef->bMeasurementStart || errorInfo.ErrCode)
		SeqExeItemExit(pSeqItem->pExeActor, errorInfo);

	ErrorInfoPack(&errorInfo, "SeqItem_MeasurementStop_Execute", "");
	return errorInfo;
}
