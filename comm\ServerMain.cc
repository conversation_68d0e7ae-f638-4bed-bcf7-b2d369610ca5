/**
 * @file ServerMain.cpp
 * <AUTHOR>
 * @brief 
 * @version 0.4.0
 * @date 2022-10-18
 * 
 * @copyright Copyright (c) 2022
 * 
 */
#include "core/core.h"
#include "core/base.h"
#include "core/rttimer.h"
#include "udpframe.h"
#include "ServerMain.h"
#include "syslog.h"
#include <stdio.h>          //printf
#include <string.h>         //memset
#include <sys/socket.h>     //socket
#include <stdlib.h>         //exit
#include <errno.h>          //errno
#include <netinet/udp.h> 
#include <netinet/ip.h>  
#include <arpa/inet.h>   
#include <fcntl.h>
#include <unistd.h>
#include "ServerComm.h"

#include <sys/ioctl.h>
#include <net/if.h>
#include <linux/ethtool.h>
#include <linux/sockios.h>
#include <ifaddrs.h>
#include <netdb.h>
#include <sys/select.h>

#include <pthread.h>
#include <netinet/tcp.h>

#define TIMEOUT         4000000
#define CONTEX_TIMEOUT  1000000

ErrorInfo	sComErrInfo;
bool bWriteComErrInfo = false;

str32   NcHmiIp = "**************";
static sRxPacket rxFrame;

int uAbsFrameIdx = 0;

str255 sth3Ip = " ";
bool bNeedModifyIp = false;
bool bExplorerConnectet = false;


#define TOPIC_BUFFER_SIZE (4096 * 4)

static byte topicTxbuf[Max_Connection_Num][TOPIC_BUFFER_SIZE];

bool bMainInitOk = false;

byte* pPushEoBuff = 0;
byte* pPushChartBuff = 0;
uint16* pushEoLen = 0;
uint16* pChartCount = 0;

int epoll_Server;


ErrorInfo GetNcHmiIp(char *pIpAddr)
{
    ErrorInfo GetErrIp;
    memset(&GetErrIp, 0, sizeof(ErrorInfo));

    struct ifaddrs* ifaddr, * ifa;
    int family, s;
    char host[NI_MAXHOST];
    s = getifaddrs(&ifaddr);
    if (s == -1)
    {
        GetErrIp.ErrCode = 12050;
        GetErrIp.eErrLever = Error;
    }
    else
    {
        for (ifa = ifaddr; ifa != NULL; ifa = ifa->ifa_next)
        {
            if (ifa->ifa_addr == NULL) continue;
            family = ifa->ifa_addr->sa_family;
            if (family == AF_INET && strcmp(ifa->ifa_name, "eth3") == 0)
            {
                s = getnameinfo(ifa->ifa_addr,
                    (family == AF_INET) ? sizeof(struct sockaddr_in) : sizeof(struct sockaddr_in6),
                    host, NI_MAXHOST,
                    NULL, 0, NI_NUMERICHOST);

                if (s != 0)
                {
                    printf("getnameinfo() failed: %s\n\n", gai_strerror(s));
                    GetErrIp.ErrCode = 12050;
                    GetErrIp.eErrLever = Error;
                }
                else
                {
                    printf("\n%s:%s\n\n", ifa->ifa_name, host);
                    memcpy(pIpAddr, &host, strlen(host)+1);
                    break;
                }
            }
        }
        freeifaddrs(ifaddr);
    }
    ErrorInfoPack(&GetErrIp, "GetNcHmiIp", "gai_strerror:%d",s);
    return GetErrIp;
}

void requestConnect(sConnection *pConnect,int indexConnect)// uint32 ip, uint16 port)
{
    pConnect->bConnected = true;
    pConnect->ConnectIndex = indexConnect;
    SendtoErrCounter[indexConnect] = 0;
    for (int j = 0; j < Max_Context_Num; j++)
    {
        if (pConnect->contexts[j].bInUse)
        {
            releaseContext(&pConnect->contexts[j]);
        }
    }
}


int requestContext(int iConnectIndex,uint32 channel)
{
    int iContexId = -1;
    if (iConnectIndex < 0)
    {
        dbgPrint(1, 0, "requestContext Error  iConnectIndex < 0 \n");
        return -1;
    }
    else
    {
        for (int i = 0; i < Max_Context_Num; i++)
        {
            if (!connections[iConnectIndex].contexts[i].bInUse)     //找到可用来缓存数据的位置
            {
                connections[iConnectIndex].contexts[i].bInUse = true;
                connections[iConnectIndex].contexts[i].ChannelID = channel;
                connections[iConnectIndex].contexts[i].iRxByteNum = 0;
                connections[iConnectIndex].contexts[i].IdxSum = -1;
                connections[iConnectIndex].contexts[i].IdxInc = 0;
                iContexId =  i;

                connections[iConnectIndex].contexts[i].FrameSum = 0;
                memset(&connections[iConnectIndex].contexts[i].aFrameIdx[0], 0, Max_Frame_Num * sizeof(int));
                break;
            }
        }
    }

    if (iContexId < 0)
        dbgPrint(1, 0, "requestContext  defeat\n");

    return iContexId;
}




void releaseAllConnect()//(uint32 ip, uint16 port)
{
    for (int i = 0; i < Max_Connection_Num; i++)
    {
        connections[i].bConnected = false;
        connections[i].ConnectIndex = -1;
        connections[i].LastUpdateTime = 0;
        for (int j = 0; j < Max_Context_Num; j++)
        {
            releaseContext(&connections[i].contexts[j]);
        }
    }
}


int findContext(int iConnectIndex, uint32 channel)
{
    if (iConnectIndex < 0)
    {
        return -1;
    }
    else
    {
        for (int i = 0; i < Max_Context_Num; i++)
        {
            if (connections[iConnectIndex].contexts[i].bInUse &&
                connections[iConnectIndex].bConnected &&
                connections[iConnectIndex].contexts[i].ChannelID == channel)
            {
                return i;
            }
        }
        return -1;
    }

}


uint32 uConnectCounter1 = 0;
uint8 uConnectCount = 0;
inline void checkTimeout(int ConnectIndex, int16 *piPhyLinked)
{
    if (connections[ConnectIndex].bConnected)      //只有在 已经连接上的情况下才会检查超时
    {
        if ((GetTimeStamp_uS() - connections[ConnectIndex].LastUpdateTime) > TIMEOUT)                   // 4S
        {
            releaseConnect(ConnectIndex);

            //释放订阅的数据
            resetTopicInList(ConnectIndex);

            dbgPrint(1, 0, "Connection[%d] is disconnected. reason: %s\n", ConnectIndex, "timeout");
        }
        else
        {
            uConnectCount++;
            for (int j = 0; j < Max_Context_Num; j++)
            {
                if (connections[ConnectIndex].contexts[j].bInUse &&
                    ((GetTimeStamp_uS() - connections[ConnectIndex].contexts[j].LastUpdateTime) > CONTEX_TIMEOUT))     // 3S
                {
                    dbgPrint(1, 0, "\n Contex TimeOut   Time:%llu > CONTEX_TIMEOUT:%d \n", 
                        (uint64)(GetTimeStamp_uS() - connections[ConnectIndex].contexts[j].LastUpdateTime),
                        (uint64)CONTEX_TIMEOUT);

                    dbgPrint(1, 0, "Contex TimeOut Connect[%d] sockClient:%d ChannelID:%d  uAbsFrameIdx:%d IdxSum:%d IdxInc:%d  iRxByteNum:%d \n\n\n",
                        ConnectIndex,
                        connections[ConnectIndex].sockClient,
                        connections[ConnectIndex].contexts[j].ChannelID,
                        uAbsFrameIdx,
                        connections[ConnectIndex].contexts[j].IdxSum,
                        connections[ConnectIndex].contexts[j].IdxInc,
                        connections[ConnectIndex].contexts[j].iRxByteNum);

                    dbgPrint(1, 0, "\n\n\n FrameSum:%d \n\n\n", connections[ConnectIndex].contexts[j].FrameSum);
                    for (int k = 0; k < connections[ConnectIndex].contexts[j].FrameSum && k < Max_Frame_Num; k++)
                    {
                        dbgPrint(1, 0, "aFrameIdx[%d] = %d\n", k, connections[ConnectIndex].contexts[j].aFrameIdx[k]);
                    }
                    releaseContext(&connections[ConnectIndex].contexts[j]);
                }
            }
        }
    }

    if (uConnectCount > 0)
        bExplorerConnectet = true;

    bConnectet = bExplorerConnectet;
}

inline ErrorCode copyContextContent(int iConnectIdx,int iContextIdx,int iChannelId, int iFrameIdx, unsigned int iAbsFrameIdx, int rxRytes)
{
    bool bRepeatFrame = false;
    connections[iConnectIdx].contexts[iContextIdx].LastUpdateTime = GetTimeStamp_uS();
    //检查是不是重复帧
    for (int i = 0; i < Max_Frame_Num; i++)
    {
        if (iFrameIdx == connections[iConnectIdx].contexts[iContextIdx].aFrameIdx[i])
        {
            dbgPrint(1, 0, "Repeat iConnectIdx[%d].iContextIdx[%d]:ChannelId[%d] FreamIdx:%d,discard\n", 
                iConnectIdx,
                iContextIdx,
                iChannelId,
                iFrameIdx);

            dbgPrint(1, 0, "FrameSum:%d\n", connections[iConnectIdx].contexts[iContextIdx].FrameSum);
            for (int k = 0; k < connections[iConnectIdx].contexts[iContextIdx].FrameSum && k < Max_Frame_Num; k++)
            {
                dbgPrint(1, 0, "aFrameIdx[%d] = %d\n", k, connections[iConnectIdx].contexts[iContextIdx].aFrameIdx[k]);
            }
            bRepeatFrame = true;
            break;
        }
    }

    if (!bRepeatFrame)
    {
        connections[iConnectIdx].contexts[iContextIdx].FrameSum++;
        connections[iConnectIdx].contexts[iContextIdx].aFrameIdx[connections[iConnectIdx].contexts[iContextIdx].FrameSum - 1] = iFrameIdx;
        if (iAbsFrameIdx >= 3)
        {
            memcpy(connections[iConnectIdx].contexts[iContextIdx].Data.Buf + FRAME_FULL_SIZE + (iAbsFrameIdx - 2) * (FRAME_FULL_SIZE - Package_Head_Length), rxFrame.data, rxRytes - Package_Head_Length);
            connections[iConnectIdx].contexts[iContextIdx].iRxByteNum += rxRytes - Package_Head_Length;
        }
        else if (iAbsFrameIdx >= 2)
        {
            memcpy(connections[iConnectIdx].contexts[iContextIdx].Data.Buf + FRAME_FULL_SIZE, rxFrame.data, rxRytes - Package_Head_Length);
            connections[iConnectIdx].contexts[iContextIdx].iRxByteNum += rxRytes - Package_Head_Length;
            //dbgPrint(1, 0, "iAbsFrameIdx:%d start addr : %d  iRxByteNum:%d\n", iAbsFrameIdx, contexts[iContextIdx].Data.Buf + RX_FRAME_FULL_SIZE, contexts[iContextIdx].iRxByteNum);
        }
        else if (iAbsFrameIdx == 1 && iFrameIdx != -1)
        {
            memcpy(connections[iConnectIdx].contexts[iContextIdx].Data.Buf, &rxFrame.ContentLen, rxRytes);
            connections[iConnectIdx].contexts[iContextIdx].iRxByteNum += rxRytes;
            //dbgPrint(1, 0, "iAbsFrameIdx:%d start addr : %d  iRxByteNum:%d\n", 
            //    iAbsFrameIdx, 
            //    connections[iConnectIdx].contexts[iContextIdx].Data.Buf, 
            //    connections[iConnectIdx].contexts[iContextIdx].iRxByteNum);
        }

        if (iFrameIdx < -1)
        {
            connections[iConnectIdx].contexts[iContextIdx].IdxSum = iAbsFrameIdx * (iAbsFrameIdx + 1) / 2;
        }
        if (iAbsFrameIdx >= 1)
        {
            //dbgPrint(1, 0, "CopyContextContent[%d][%d]  CopyDeltaTime:%f \n",
            //    iConnectIdx,
            //    iConnectIdx,
            //    (float)(gSysTimestamp_uS - connections[iConnectIdx].contexts[iContextIdx].LastUpdateTime)/1000000.0);
            connections[iConnectIdx].contexts[iContextIdx].IdxInc += iAbsFrameIdx;
            // connections[iConnectIdx].contexts[iContextIdx].LastUpdateTime = gSysTimestamp_uS;
        }

        return 0;
    }
    else
    {
        return -1;
    }
}

uint8 ConnectedCount = 0;
uint8 commState = 0;

uint32 uConnectCounter = 0;
const char* SysCommStateName[] = {"udp_Mac_Init","udp_State_Init","udp_State_Wait","udp_State_work","udp_State_reLinkCheck","udp_State_reBind"};

/*GetLinkStatus
* @param[in]     const char* ifname
* @param[Out]    INT16      
* @return        INT16              //-1:检测失败       0:物理连接断开     1:物理连接正常    
*/
int16 GetLinkStatus(const char* ifname) 
{
    int16 sockfd = socket(AF_INET, SOCK_DGRAM, 0);
    if (sockfd < 0) return -1;

    struct ifreq ifr;
    struct ethtool_value edata;
    memset(&ifr, 0, sizeof(ifr));
    strncpy(ifr.ifr_name, ifname, IFNAMSIZ - 1);

    edata.cmd = ETHTOOL_GLINK;
    ifr.ifr_data = (char*)&edata;

    if (ioctl(sockfd, SIOCETHTOOL, &ifr) == -1) {
        close(sockfd);
        return -1;
    }

    close(sockfd);
    return edata.data;
}


struct sockaddr_in sockServe;
epoll_event event_ServerSet;

epoll_event serverEvents[MAX_EVENTS];
int sock_Server = -1;
int trigEventNum;



socklen_t iaddrlen = sizeof(sockaddr_in);


/*findClientIndex             //找到可以用的 client 索引
* @return        int
*/
int findClientIndex()
{
    int iClientInde = -1;
    for (uint8 i = 0; i < Max_Connection_Num; i++)
    {
        if (!connections[i].bTcpHandShakeOk)
        {
            iClientInde = i;
            break;
        }

    }

    return iClientInde;
}
/*ClientManage             //监控服务器的sock 的epoll 管理多连接
* @return        int
*/
int ClientManage()
{
    trigEventNum = epoll_wait(epoll_Server, serverEvents, 1024, 0);// 5000);
    if (trigEventNum == -1)
    {
        dbgPrint(1, 0, "epoll_wait trigEventNum  -1  errno:%d \n", errno);
        return -1;
    }
    else if (trigEventNum == 0)
    {
        return 0;
    }
    else
    {
        int clientIndex = -1;
        for (int16 i = 0; i < trigEventNum; i++)
        {
            if (serverEvents[i].events == EPOLLIN)        
            {
                int tmpFd = serverEvents[i].data.fd;
                if (tmpFd == sock_Server)                   //判断 服务器网口收到新的请求
                {
                    clientIndex = findClientIndex();
                    if (clientIndex >= 0 && clientIndex < Max_Connection_Num)
                    {
                        connections[clientIndex].sockClient = accept(sock_Server, (struct sockaddr*)&connections[clientIndex].sockAddr, &iaddrlen);
                        if (connections[clientIndex].sockClient < 0)
                        {
                            // 处理其他错误
                            printf("sock_Client < 0   errno:%d\n", errno);
                        }
                        else
                        {
                            // 创建 epoll 实例
                            connections[clientIndex].epollClient = epoll_create1(0);
                            if (connections[clientIndex].epollClient == -1) {
                                perror("epoll_create1");
                                close(connections[clientIndex].epollClient);
                            }

                            // 注册监听当前客户端的  套接字到 epoll
                            connections[clientIndex].event_Set.events = EPOLLIN | EPOLLET;                  // 边缘触发模式
                            connections[clientIndex].event_Set.data.fd = connections[clientIndex].epollClient;
                            if (epoll_ctl(connections[clientIndex].epollClient, EPOLL_CTL_ADD, connections[clientIndex].sockClient, &connections[clientIndex].event_Set) == -1)
                            {
                                perror("epoll_ctl: event_ClientSet");
                                close(connections[clientIndex].sockClient);
                                close(connections[clientIndex].epollClient);
                            }
                            else
                            {
                                connections[clientIndex].LastUpdateTime = GetTimeStamp_uS();
                                connections[clientIndex].bTcpHandShakeOk = true;
                                printf("events == EPOLLIN sock_Client[%d].sockClient:%d\n",clientIndex, connections[clientIndex].sockClient);
                            }
                        }
                    }
                    else
                    {
                        // 打印当前连接状态用于调试
                        printf("=== No available slots, current connections status ===\n");
                        for (int debug_i = 0; debug_i < Max_Connection_Num; debug_i++)
                        {
                            printf("connections[%d]: bTcpHandShakeOk=%d, bConnected=%d, sockClient=%d, LastUpdateTime=%llu\n",
                                   debug_i,
                                   connections[debug_i].bTcpHandShakeOk,
                                   connections[debug_i].bConnected,
                                   connections[debug_i].sockClient,
                                   connections[debug_i].LastUpdateTime);
                        }

                        // 没有可用连接槽位，但仍需accept以清空监听队列
                        struct sockaddr_in tempAddr;
                        socklen_t tempAddrLen = sizeof(tempAddr);
                        int tempSock = accept(sock_Server, (struct sockaddr*)&tempAddr, &tempAddrLen);
                        if (tempSock >= 0)
                        {
                            close(tempSock);  // 立即关闭连接
                            printf("Connection rejected: no available slots (tempSock:%d)\n", tempSock);
                        }
                        else
                        {
                            printf("Accept failed when no slots available, errno:%d\n", errno);
                        }
                    }
                }
                else
                {
                    printf("events != EPOLLIN   trigEventNum:%d [%d] accept Ok. sock_Client:%d\n", trigEventNum, i, connections[clientIndex].sockClient);
                }
            }
            else if (serverEvents[i].events == EPOLLOUT)
            {
                printf("\n\n serverEvents[%d]  EPOLLOUT \n\n", i);

                struct sockaddr tmpSockAddr;
                int tmpSock = accept(sock_Server, (struct sockaddr*)&tmpSockAddr, &iaddrlen);
                for (int m = 0; m < Max_Connection_Num; m++)
                {
                    if (connections[m].bTcpHandShakeOk &&
                        connections[m].sockClient == tmpSock)
                    {
                        close(connections[m].sockClient);
                        close(connections[m].epollClient);
                        connections[m].bTcpHandShakeOk = false;
                        connections[m].bConnected = false;
                    }
                }
            }

        }
    }
    return trigEventNum;
}

int64  PushPointBuffAddr;
int32* pPushPointsCount;
int32* pPushHadPushPoints;

int16 iPhyLinked = -1;
bool  bMultiFrame = false;
void* comm_thread(void* arg)
{
    udp_State state = udp_Mac_Init;
    udp_State Laststate = udp_Mac_Init;
    int ret;
    int32 nRxBytes, nTxBytes;
    byte* pRecvBuf;// [3] ;
    int iRecvBufLength;// [3] ;
    int  resolvedLength;
    int iContextIdx ;

    sTxPacket* pTxFrame;
    volatile int64 commStart_us;
    volatile float64  commExecTime_uS;

    bMainInitOk = false;
    uint64 gLinkCheckCounter = 0;
    uint64 uCommCounter = 0;

    while (1)
    {
        commStart_us = GetSysTick();
        memset(&sComErrInfo, 0, sizeof(ErrorInfo));
        uCommCounter++;
        switch (state)
        {
        case udp_Mac_Init:
        {
            iPhyLinked = GetLinkStatus("eth3");
            if ((SYSTEM_MOTION_MODE == 1 && iPhyLinked == 1)||      //控制器模式 监控网口有没有插上才开始进行通讯
                (SYSTEM_MOTION_MODE == 0))                          //监控仪模式 因为是内部通讯  允许不物理连接网口
            {
                sock_Server = -1;
                state = udp_State_Init;
            }
            else
            {
                state = udp_State_reLinkCheck;
            }
        }
        break;
        case udp_State_Init: 
        {
            if (bMainInitOk)     
            {
                bExplorerConnectet = false;
                bWriteComErrInfo = false;
                nRxBytes = 0;
                nTxBytes = 0;
                resolvedLength = 0;
                iContextIdx = -1;
                if (sock_Server != -1)
                    close(sock_Server);

                //sock = socket(AF_INET, SOCK_DGRAM, 0);    //udp
                sock_Server = socket(AF_INET, SOCK_STREAM, 0);     //tcp
                if (sock_Server < 0) {
                    dbgPrint(1, 0, "Create socket was failed\n");
                    state = udp_State_reLinkCheck;
                }
                else
                {
                    int flags = fcntl(sock_Server, F_GETFL, 0);                                          //获取文件的flags值。
                    ret = fcntl(sock_Server, F_SETFL, flags | O_NONBLOCK);                               //设置成非阻塞模式;
                    if (ret < 0)
                    {
                        dbgPrint(1, 0, "fcntl Error Ip:%s   errno:%d\n", NcHmiIp, errno);
                        gLinkCheckCounter = 0;

                        sComErrInfo.eErrLever = MjError;
                        sComErrInfo.ErrCode = 12021;
                        state = udp_State_reBind;
                    }
                    else
                    {
                        //TCP_NODELAY;
                        int opt = 1;
                        setsockopt(sock_Server, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt));
                        bzero(&sockServe, sizeof(struct sockaddr_in));

                        //禁用 Nagle 算法（减少发送端合并）
                        opt = 1;
                        setsockopt(sock_Server, IPPROTO_TCP, TCP_NODELAY, &opt, sizeof(opt));

                        sockServe.sin_family = AF_INET;
                        sockServe.sin_addr.s_addr = inet_addr(NcHmiIp);
                        sockServe.sin_port = htons(8080);

                        ret = bind(sock_Server, (struct sockaddr*)&sockServe, iaddrlen);
                        InitRtCurveFifo();
                        if (ret < 0)
                        {
                            dbgPrint(1, 0, "NcHmiIp Ip:%s   bind:%d  < 0\n", NcHmiIp, ret);
                            gLinkCheckCounter = 0;

                            sComErrInfo.eErrLever = MjError;
                            sComErrInfo.ErrCode = 12021;
                            state = udp_State_reBind;
                        }
                        else
                        {
                            for (int i = 0; i < Max_Connection_Num; i++)
                            {
                                memset(connections + i, 0, sizeof(sConnection));
                            }
                                
                            ret = listen(sock_Server, Max_Connection_Num);                      //listen
                            if (ret < 0)
                            {
                                dbgPrint(1, 0, "listen Error Ip:%s   bind:%d  < 0  errno:%d\n", NcHmiIp, ret, errno);
                                state = udp_State_reBind;
                                sComErrInfo.eErrLever = MjError;
                                sComErrInfo.ErrCode = 12022;
                            }
                            else
                            {
                                //dbgPrint(1, 0, "listen Ok return：%d\n", ret);
                                // 创建 epoll 实例
                                epoll_Server = epoll_create1(0);
                                if (epoll_Server == -1) {
                                    perror("epoll_create1");
                                    close(sock_Server);
                                    exit(EXIT_FAILURE);
                                }

                                // 注册监听套接字到 epoll
                                event_ServerSet.events = EPOLLIN | EPOLLOUT | EPOLLET;  // 边缘触发模式
                                event_ServerSet.data.fd = sock_Server;
                                if (epoll_ctl(epoll_Server, EPOLL_CTL_ADD, sock_Server, &event_ServerSet) == -1)
                                {
                                    perror("epoll_ctl: server_fd");
                                    close(sock_Server);
                                    close(epoll_Server);
                                    exit(EXIT_FAILURE);
                                }

                               // iConnIdx = -1;
                                state = udp_State_Wait;
                            }
                        }
                    }
                }
            }
        } break;
        case udp_State_Wait:
        {
            state = udp_State_work;
        }
        break;
        case udp_State_work:
        {
            if ((SYSTEM_MOTION_MODE == 1 && iPhyLinked == 1) ||      //控制器模式 监控网口有没有插上才开始进行通讯
                SYSTEM_MOTION_MODE == 0)                             //监控仪模式 因为是内部通讯  允许不物理连接网口
            {
                if (bNeedModifyIp)
                {
                    dbgPrint(1, 0, "sModifyIp Rtrig Ip:%s\n", sth3Ip);
                    uint8 uHmiIpLen = strlen(sth3Ip) + 1;
                    if (uHmiIpLen >= 8 && uHmiIpLen < sizeof(str32))
                    {
                        for (int iConnectIdx = 0; iConnectIdx < Max_Connection_Num; iConnectIdx++)
                        {
                            releaseConnect(iConnectIdx);
                        }
                        memset(NcHmiIp, 0, sizeof(str32));
                        memcpy(NcHmiIp, sth3Ip, strlen(sth3Ip));
                        state = udp_State_Init;
                        dbgPrint(1, 0, "sModifyIp   udp_State_Init Ip:%s bMainInitOk:%s\n", NcHmiIp, bMainInitOk ? "true" : "false");
                    }
                    else
                    {
                        sComErrInfo.eErrLever = Warm;
                        sComErrInfo.ErrCode = 12019;
                    }
                    bNeedModifyIp = false;
                }
                else
                {
                    uConnectCount = 0;
                    //监控服务器的sock 的epoll 管理多连接
                    ClientManage();

                    SendSeqCurveAndEoResult(PushPointBuffAddr, pPushPointsCount, pPushHadPushPoints, pPushEoBuff, pushEoLen);
                    for (int j = 0; j < Max_Connection_Num; j++)
                    {
                        if (connections[j].bTcpHandShakeOk)
                        {
                            trigEventNum = epoll_wait(connections[j].epollClient, connections[j].Events, 1024, 0);// 5000);
                            if (trigEventNum == -1)
                            {
                                dbgPrint(1, 0, "connections[%d]  trigEventNum:%d\n", j, trigEventNum);
                                close(connections[j].epollClient);
                                releaseConnect(j);
                                //释放订阅的数据
                                resetTopicInList(j);
                                connections[j].bTcpHandShakeOk = false;
                                connections[j].bConnected = false;
                            }
                            else if (trigEventNum == 0)     //长期没有收到  客户端的信息 应该将客户端的epoll注销掉
                            {
                                if ((GetTimeStamp_uS() - connections[j].LastUpdateTime) > TIMEOUT)     // 4S
                                {
                                    dbgPrint(1, 0, "close epoll[%d]  sockClient:%d  delataTime:%llu\n", 
                                        j,connections[j].sockClient,
                                        (GetTimeStamp_uS() - connections[j].LastUpdateTime));

                                    close(connections[j].epollClient);

                                    releaseConnect(j);

                                    //释放订阅的数据
                                    resetTopicInList(j);
                                    connections[j].bTcpHandShakeOk = false;
                                    connections[j].bConnected = false;
                                }
                            }
                            else
                            {
                                connections[j].LastUpdateTime = GetTimeStamp_uS();
                                //推送订阅数据
                                SendTopicData(connections + j);

                                do {//一次性处理所有堆积的包
                                    nRxBytes = recv(connections[j].sockClient, (char*)&rxFrame.ContentLen, 2, MSG_DONTWAIT);
                                    if (nRxBytes > 0)
                                    {
                                        if (nRxBytes == 2 && rxFrame.ContentLen <= 1410)  //先读取当前帧的 数据长度
                                        {
                                           // dbgPrint(1, 0, "nRxBytes:%d  ContentLen:%d\n", nRxBytes, rxFrame.ContentLen);
                                            nRxBytes = recv(connections[j].sockClient, (char*)&rxFrame.subFrameIdx, rxFrame.ContentLen, MSG_DONTWAIT);
                                            if (nRxBytes == rxFrame.ContentLen)
                                            {
                                                rxFrame.iFrameLength = nRxBytes + 2;
                                                pTxFrame = GetTxFrame();
                                            }
                                            else
                                            {
                                                uint16 uCounterRecv = 0;
                                                uint16 uExeCounter = 0;
                                                rxFrame.iFrameLength = nRxBytes;

 /*                                               dbgPrint(1, 0, "uExeCounter:%d Counter:%d  CurRecvLen:%d  iFrameLength:%d ContentLen:%d\n",
                                                    uExeCounter, uCounterRecv,
                                                    nRxBytes,
                                                    rxFrame.iFrameLength,
                                                    rxFrame.ContentLen);*/

                                                do//需要做一个超时保护  防止死这里面了
                                                {
                                                    uExeCounter++;
                                                    nRxBytes = recv(connections[j].sockClient, (char*)&rxFrame.subFrameIdx + rxFrame.iFrameLength, rxFrame.ContentLen, MSG_DONTWAIT);
                                                    if (nRxBytes > 0)
                                                    {
                                                        uCounterRecv++;
                                                        rxFrame.iFrameLength += nRxBytes;
 /*                                                       dbgPrint(1, 0, "uExeCounter:%d Counter:%d  CurRecvLen:%d  iFrameLength:%d ContentLen:%d\n", 
                                                            uExeCounter, uCounterRecv, 
                                                            nRxBytes, 
                                                            rxFrame.iFrameLength, 
                                                            rxFrame.ContentLen);*/
                                                    }
                                                    else
                                                    {
                                                        continue;
                                                    }

                                                } while (rxFrame.iFrameLength != rxFrame.ContentLen);

                                                rxFrame.iFrameLength += 2;

                                                dbgPrint(1, 0, "Finish iFrameLength:%d ContentLen:%d  subFrameIdx:%d FrameType:%d \n\n", 
                                                    rxFrame.iFrameLength, 
                                                    rxFrame.ContentLen,
                                                    rxFrame.subFrameIdx,
                                                    rxFrame.FrameType);
                                                //这里数据没有读完全 
                                            }
                                        }
                                        else
                                        {
                                            dbgPrint(1, 0, "2 Read Len Error  nRxBytes:%d  ContentLen:%d\n", nRxBytes, rxFrame.ContentLen);
                                            break;
                                        }

                                        connections[j].LastUpdateTime = GetTimeStamp_uS();        //刷新连接时间
                                        if (rxFrame.subFrameIdx == -1 && rxFrame.FrameType == 0x20 && *(uint16*)rxFrame.data == 1) //请求连接
                                        {
                                            if (!((((connections[j].sockAddr.sin_addr.s_addr >> 0) & 0xFF) == 0) &&
                                                (((connections[j].sockAddr.sin_addr.s_addr >> 8) & 0xFF) == 0) &&
                                                (((connections[j].sockAddr.sin_addr.s_addr >> 16) & 0xFF) == 0) &&
                                                (((connections[j].sockAddr.sin_addr.s_addr >> 24) & 0xFF) == 0)))
                                            {
                                                //新的连接 建立连接
                                                resetTopicInList(j);
                                                *(uint16*)pTxFrame->data = 0;
                                                dbgPrint(1, 0, "Handshake %s. Connection[%d] is established. %d.%d.%d.%d:%d  Repeat:%s\n", "OK", j,
                                                    (connections[j].sockAddr.sin_addr.s_addr >> 0) & 0xFF,
                                                    (connections[j].sockAddr.sin_addr.s_addr >> 8) & 0xFF,
                                                    (connections[j].sockAddr.sin_addr.s_addr >> 16) & 0xFF,
                                                    (connections[j].sockAddr.sin_addr.s_addr >> 24) & 0xFF,
                                                    htons(connections[j].sockAddr.sin_port),
                                                    connections[j].bConnected ? "Yes" : "No ");

                                                pTxFrame->iFrameLength = Package_Head_Length + 2;
                                                pTxFrame->ContentLen = pTxFrame->iFrameLength - 2;

                                                pTxFrame->subFrameIdx = -1;
                                                pTxFrame->ChannelID = rxFrame.ChannelID;
                                                pTxFrame->FrameType = 0x8020;
                                                sComErrInfo = SendResponseFrameByFd(connections[j].sockClient, &connections[j].sockAddr, pTxFrame, &nTxBytes);

                                                requestConnect(connections + j, j);
                                                continue;
                                            }
                                        }
                                        else   //没有连上 收到无用数据
                                        {
                                            uAbsFrameIdx = abs(rxFrame.subFrameIdx);
                                            if (rxFrame.subFrameIdx == -1)
                                            {
                                                pRecvBuf = (byte*)&rxFrame;
                                                iRecvBufLength = rxFrame.iFrameLength;
                                            }
                                            else                                                //数据长，分帧处理
                                            {
                                                pRecvBuf = 0;
                                                iContextIdx = findContext(j, rxFrame.ChannelID);        //查询缓存帧位置
                                                if (iContextIdx < 0)
                                                {
                                                    iContextIdx = requestContext(j, rxFrame.ChannelID); //分配新的帧位置
                                                }

                                                //if (((connections[j].sockAddr.sin_addr.s_addr >> 24) & 0xFF) == 99)
                                                //{
                                                //    uint64 uTime = GetTimeStamp_uS();
                                                //    dbgPrint(1, 0, "Context！  IP:%d.%d.%d.%d:%d  ChannelID:%d subFrameIdx:%d nRxBytes:%d time:%llu\n",
                                                //        (connections[j].sockAddr.sin_addr.s_addr >> 0) & 0xFF,
                                                //        (connections[j].sockAddr.sin_addr.s_addr >> 8) & 0xFF,
                                                //        (connections[j].sockAddr.sin_addr.s_addr >> 16) & 0xFF,
                                                //        (connections[j].sockAddr.sin_addr.s_addr >> 24) & 0xFF,
                                                //        htons(connections[j].sockAddr.sin_port),
                                                //        rxFrame.ChannelID,
                                                //        rxFrame.subFrameIdx,
                                                //        nRxBytes,
                                                //        uTime);
                                                //}

                                                if (iContextIdx < 0)        //缓存位置被占满
                                                {
                                                    dbgPrint(1, 0, "Can't allocate context！  IP:%d.%d.%d.%d:%d  ChannelID:%d subFrameIdx:%d nRxBytes:%d\n",
                                                        (connections[j].sockAddr.sin_addr.s_addr >> 0) & 0xFF,
                                                        (connections[j].sockAddr.sin_addr.s_addr >> 8) & 0xFF,
                                                        (connections[j].sockAddr.sin_addr.s_addr >> 16) & 0xFF,
                                                        (connections[j].sockAddr.sin_addr.s_addr >> 24) & 0xFF,
                                                        htons(connections[j].sockAddr.sin_port),
                                                        rxFrame.ChannelID,
                                                        rxFrame.subFrameIdx,
                                                        rxFrame.iFrameLength);
                                                }
                                                else
                                                {
                                                    if (uAbsFrameIdx <= Max_Context_Size / FRAME_BODY_FULL_SIZE)
                                                        copyContextContent(j, iContextIdx, rxFrame.ChannelID, rxFrame.subFrameIdx, uAbsFrameIdx, rxFrame.iFrameLength);
                                                    else
                                                    {
                                                        sComErrInfo.ErrCode = 1200;
                                                        sComErrInfo.eErrLever = Error;
                                                        releaseContext(&connections[j].contexts[iContextIdx]);
                                                        //assert("uAbsFrameIdx Out MaxSize");
                                                    }


                                                    if ((connections[j].contexts[iContextIdx].IdxSum == connections[j].contexts[iContextIdx].IdxInc)&&
                                                        sComErrInfo.ErrCode == 0)
                                                    {
                                                        releaseContext(&connections[j].contexts[iContextIdx]);
                                                        pRecvBuf = connections[j].contexts[iContextIdx].Data.Buf;
                                                        iRecvBufLength = connections[j].contexts[iContextIdx].iRxByteNum;

                                                        //dbgPrint(1, 0, "iRxByteNum:%d\n", iRecvBufLength);
                                                    }
                                                }
                                            }
                                        }

                                        if (sComErrInfo.ErrCode == 0 &&
                                            pRecvBuf && 
                                            iRecvBufLength > 0 &&
                                            connections[j].bConnected)
                                        {
                                            sRxPacket* pCurFrame = (sRxPacket*)pRecvBuf;
                                            sComErrInfo = ResolverFrame(connections + j, (sRxPacket*)pRecvBuf, iRecvBufLength, &resolvedLength);
    /*                                        if (bMultiFrame)
                                                goto MultiFreamDeal;*/
                                        }
                                    }
                                    else
                                    {
                                        continue;
                                    }
                                } while (nRxBytes > 0);
                            }
                        }
                        else
                        {
                            if (connections[j].bConnected)
                            {
                                dbgPrint(1, 0, "!bTcpHandShakeOk && bConnected[%d]\n\n", j);
                                close(connections[j].epollClient);
                                releaseConnect(j);
                                //释放订阅的数据
                                resetTopicInList(j);
                            }
                        }
                        //超时检测
                        checkTimeout(j, &iPhyLinked);
                    }
                }
            }
            else
            {
                state = udp_State_reLinkCheck;                       //等待重连
            }


        } break;
        case udp_State_reLinkCheck:
            gLinkCheckCounter++;
            if (gLinkCheckCounter % 1000 == 0)
            {
                iPhyLinked = GetLinkStatus("eth3");
                rt_dbgPrint(1, 0, "GetLinkStatus:%d \n", iPhyLinked);
            }

            if (iPhyLinked == 1)
            {
                state = udp_State_Init;
            }
            break;
        case udp_State_reBind:
            gLinkCheckCounter++;
            if (gLinkCheckCounter % 3000 == 0)
            {
                gLinkCheckCounter = 0;
                ret = bind(sock_Server, (struct sockaddr*)&sockServe, sizeof(sockServe));
                if (ret)
                    state = udp_State_Init;
            }
            break;
        default:
            break;
        }

        if (state != Laststate)
        {
            //rt_dbgPrint(1, 0, "CommState:[%s -> %s]\n", SysCommStateName[Laststate], SysCommStateName[state]);
            Laststate = state;
        }

        ErrorInfoPack(&sComErrInfo, "comm_thread", "");

        commExecTime_uS = (GetSysTick() - commStart_us) / 1000;
        if (commExecTime_uS < 1000)
            Sleep_uS(1000 - commExecTime_uS);
    }

    if (sock_Server) {
        close(sock_Server);
    }
    return 0;
}
