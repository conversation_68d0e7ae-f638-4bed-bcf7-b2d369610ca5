#include <pthread.h>
#include "RtThread.h"
#include "rttimer.h"
#include <unistd.h>
#include "MainWork.h"
#include "Sensor.h"
#include "SysShareMemoryDefine.h"
#include "ec_app.h"
#include "SysShareMemoryOp.h"
#include <stdio.h>
#include <stdlib.h>
#include "FieldbusThread.h"
#include "SysVarDefine.h"
#include "motion.h"
#include "sequence/SeqItemMeasurement.h"

bool bEthercatInitBusy = false;
bool bEtherCatInitError = false;
ErrorInfo EthcatInitErr;

int	iSpiUpdateResult;

float32 dTmpPosNoFilter;
float32 fTmpVelNoFilter;
float32 dTmpRevolutionTranlatepara;
float32 fVelFbk;
void MC_RefreshPara(Axis_Para* pAxis)
{
	if (pSysShareData->sExSW.eSysState > Sys_State_Init)
	{
		MotorPara* pMotor = &pAxis->Motor;
		float32 MotorDeltaPoint = ((float32)pMotor->LinkVar.PosFbk - (float32)pMotor->LastPosFbk);

		dTmpRevolutionTranlatepara = ((float32)pAxis->Cfg.ShaftPlusePerRevolution) / pAxis->Cfg.Tranlatepara;

		dTmpPosNoFilter = ((float32)pMotor->LinkVar.PosFbk) / dTmpRevolutionTranlatepara;
		pAxis->LogicFbk.fPosNoFilter = (float32)dTmpPosNoFilter + pAxis->Cfg.LogicPositionOffset;
		pAxis->LogicFbk.fPos = MoveAvgFilter(&pAxis->Context.PosFbkCalcFlt, pAxis->LogicFbk.fPosNoFilter);

		fVelFbk = (float32)pMotor->LinkVar.VelFbk;
		fTmpVelNoFilter = fVelFbk / dTmpRevolutionTranlatepara;

		pAxis->LogicFbk.fVelNoFilter = fTmpVelNoFilter;
		pAxis->LogicFbk.fVel = MoveAvgFilter(&pAxis->Context.velFbkCalcFlt, pAxis->LogicFbk.fVelNoFilter);

		pAxis->LogicFbk.fAccNoFilter = (pAxis->LogicFbk.fVelNoFilter - pAxis->Context.lastLogicFbkVel) / pAxis->SystemPeriod;
		pAxis->LogicFbk.fAcc = MoveAvgFilter(&pAxis->Context.AccFbkCalcFlt, pAxis->LogicFbk.fAccNoFilter);
	}

	pAxis->Motor.bIsError = (pAxis->Motor.LinkVar.SW >> 3) & 1;
	pAxis->Motor.bIsPowerOn = (pAxis->Motor.LinkVar.SW & 0x237) == 0x237;;

	if (MC_IsServoInError(pAxis))
	{
		pAxis->Context.bWorkProfileAviable = false;
	}
}



float64 InExeTime;
uint64 OutStartTick;
float64 OutExeTime;
void EtherCatManager()
{
	if (SYSTEM_MOTION_MODE && (!bEthercatHadInit || pSysShareData->bNeedResetMaster))
	{
		EthcatInitErr = EthercatMasterInit("eth1", &bEthercatInitBusy);//GMAC0  
		if (!bEthercatInitBusy)
		{
			if (EthcatInitErr.ErrCode)
			{
				bEtherCatInitError = true;
			}
			else
			{
				//dbgPrint(1, 0, "bEthercatHadInit\n");
				bEthercatHadInit = true;
				bEtherCatInitError = false;
			}
			pSysShareData->bNeedResetMaster = false;
		}
	}

	if (SYSTEM_MOTION_MODE && (!bEtherCatInitError || bEtherCatHandShake))
	{
		ec_master_period_data_out();

		MC_RefreshPara(&pSysShareData->AxisMc[0].Axis);

		ec_master_period_data_in();
	}
}

__useconds_t waitTime_us;
uint32 lostCounter = 0;
void* EtherCatThread(void* arg)
{
	dorun = 0;
	bEthercatHadInit = false;
	while (1)
	{
		pSysShareData->gSysCounter++;       //带控制的放到这里计数  监控仪放到RealtimeFunction计数

		EtherCat_timer_handle(&pSysShareData->gSysCounter, &pSysShareData->EtherCat_ThreadExecInfo);

		if (dorun == 0 && !bEtherCatHandShake)
		{
			waitTime_us = (__useconds_t)(pSysShareData->EtherCat_ThreadExecInfo.RunPeriod_uS);
			EtherCat_Sleep(waitTime_us);
		}
		else
		{
			if ((pSysShareData->EtherCat_ThreadExecInfo.WaitTime_uS) > 0 &&
				(pSysShareData->EtherCat_ThreadExecInfo.WaitTime_uS < 10 * pSysShareData->EtherCat_ThreadExecInfo.RunPeriod_uS))//保护
			{
				waitTime_us = (__useconds_t)pSysShareData->EtherCat_ThreadExecInfo.WaitTime_uS;
				EtherCat_Sleep(waitTime_us);
			}
			else
			{
				if ((dorun > 0))
				{
					lostCounter++;
					//dbgPrint(1, 0, "dorun:%d  bEtherCatHandShake:%s lostCounter:%d  NextPeriod_uS:%lf  ExecTime_uS:%lf InStartTick:%lf  OutExeTime:%lf  uTestItem:%d\n\n",
					//    dorun,
					//    bEtherCatHandShake ? "true " : "false",
					//    lostCounter,
					//    pSysShareData->EtherCat_ThreadExecInfo.NextPeriod_uS,
					//    pSysShareData->EtherCat_ThreadExecInfo.ExecTime_uS,
					//    InExeTime,
					//    OutExeTime,
					//    pSysShareData->uTestItem);
				}
			}
		}
	}
}

void RealtimeFunction()
{
	if (SYSTEM_MOTION_MODE == 0)     //监控仪放到这里计数   带控制的放到EtherCat线程 计数
		pSysShareData->gSysCounter++;

	if (pSysShareData->gSysCounter % 400 == 0)
	{
		GetSystemTime();			//这个必须要一直刷

		GetTimeStr((char*)&sSysDeviceSetInfo.DispTime);
	}

	iSpiUpdateResult = SpiDataUpdate(&pSysShareData->bHadInitSensSpi, &pSysShareData->bSpiSensConnected);

	rt_calc(&iSpiUpdateResult);
}


__useconds_t RtWaitTime_us;
void* MainRtThread(void* arg)
{
	eFieldbusState = 0;
	pSysShareData->rt_ThreadExecInfo.pFunc = RealtimeFunction;
	pSysShareData->rt_ThreadExecInfo.RunPeriod_uS = SYS_BASE_TIME_uS;
	while (1)
	{
		rt_timer_handle(&pSysShareData->gSysCounter, &pSysShareData->rt_ThreadExecInfo);
		if (pSysShareData->rt_ThreadExecInfo.WaitTime_uS > 0)
		{
			RtWaitTime_us = (__useconds_t)pSysShareData->rt_ThreadExecInfo.WaitTime_uS;
			Sleep_uS(RtWaitTime_us);
		}
	}
}


pthread_t StartPressRtThread(int thread_priority, int32 uRunPeriod_uS, CycleFunction* pFunc, ThreadExecInfo* pThreadInfo) {
	pthread_t tid;
	struct sched_param rtparam = { .sched_priority = thread_priority };
	pthread_attr_t rtattr;
	int ret;

	//初始化线程属性 
	pthread_attr_init(&rtattr);

	//设置线程为非分离线程    //只有当线程pthread_join时才终止自己
	pthread_attr_setdetachstate(&rtattr, PTHREAD_CREATE_JOINABLE);

	//设置线程不继承父线程的调度策略
	pthread_attr_setinheritsched(&rtattr, PTHREAD_EXPLICIT_SCHED);
#if (defined PREEMPT) || (defined XENOMAI)
	pthread_attr_setschedpolicy(&rtattr, SCHED_OTHER);// SCHED_FIFO);           //设置线程的调度策略
#endif 

	pthread_attr_setschedparam(&rtattr, &rtparam);
	pThreadInfo->pFunc = pFunc;
	pThreadInfo->RunPeriod_uS = uRunPeriod_uS;

	if (pthread_create(&tid, &rtattr, MainRtThread, pThreadInfo)) {
		dbgPrint(1, 0, "create pthread failed\n");
	}
	return tid;
}


pthread_t StartEtherCatThread(int thread_priority, int32 uRunPeriod_uS, CycleFunction* pFunc, ThreadExecInfo* pThreadInfo) {
	pthread_t tid;
	struct sched_param rtparam = { .sched_priority = thread_priority };
	pthread_attr_t rtattr;
	int ret;

	//初始化线程属性 
	pthread_attr_init(&rtattr);

	//设置线程为非分离线程    //只有当线程pthread_join时才终止自己
	pthread_attr_setdetachstate(&rtattr, PTHREAD_CREATE_JOINABLE);

	//设置线程不继承父线程的调度策略
	pthread_attr_setinheritsched(&rtattr, PTHREAD_EXPLICIT_SCHED);
#if (defined PREEMPT) || (defined XENOMAI)
	pthread_attr_setschedpolicy(&rtattr, SCHED_FIFO);           //设置线程的调度策略
#endif 
	pthread_attr_setschedparam(&rtattr, &rtparam);
	pThreadInfo->RunPeriod_uS = uRunPeriod_uS;
	pThreadInfo->pFunc = pFunc;

	if (pthread_create(&tid, &rtattr, EtherCatThread, pThreadInfo)) {
		dbgPrint(1, 0, "create pthread failed\n");
	}
	return tid;
}


