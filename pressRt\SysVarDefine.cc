#include "SysVarDefine.h"
#include "core/core.h"
#include "core/rttimer.h"
#include "sequence/Sequence.h"

#include "EoThread.h"
#include "Sn.h"
#include "ChannelAndChart.h"
#include "Statistics.h"
#include "comm/Protocol.h"
#include "comm/ServerMain.h"
#include "Global&LocalVar.h"
#include "EO.h"
#include "SysShareMemoryDefine.h"
#include "DataBaseThread.h"
#include "SystemProtect.h"
#include "sqliteapi/sqliteOp/SqliteOp.h"
#include "float.h"
#include "sequence/SeqItemMeasurement.h"

uint8 uSystemMode = SYSTEM_MOTION_MODE;
uint8 uEcoMoveMode = SYSTEM_MOVE_SERIES;//0:EcoMove;1:AccMove
uint8 uCurveMode = 0;

uint16   PremissionSet = 0;

SeqRef              Seq;
bool			    bSysInitOp = false;
//当年系统时间   每次更新软件时 更新当前时间，用作系统时间异常的检测

SysInfoData SysInfo =
{
	.ControlSoftwareVersion = "2.0.0 20250724",
	.DisplaySoftwareVersion = "",
	.HardwareVersion = "lx-press",
	.FieldbusVersion = "V1.0.0",
	.ProfileVersion = "V1.0.0",
	.ResultFileVersion = "V1.0.0",
	.sSqliteOpVersion = opversion,
	.sSqliteWebVersion = webapiversion
};

DeviceSetInfo           sSysDeviceSetInfo;
SSensorData				sPrfForceData;					                //多个压力传感器
SSensorData				sPrfExtPosData;			                        //Potentiometer
SSensorData				sPrfExtSensorsData;		                        //其他传感器参数

SSensorData				sActiveForceData;							    //激活的 压力传感器
SSensorData				sActiveExtPosData;								//激活的 Potentiometer
SSensorData				sActiveSensorsData;								//激活的 其他传感器参数

SSensorsPara			sSysFactoryLimitPara;                           //传感器出厂 参数
SSensorsPara			sSysSetLimitPara;                               //传感器设定 参数     从各个扫描到的传感器中收集上来

uint64 lSdoSaveCounter = 0;


SDOEntryDesc entryGlobalVar[51];
SDOEntryDesc entryLocalVar[51];

float32 aEoResult[50];
float32 aEoPosition[50];

ThreadExecInfo      mainRtThreadInfo;

bool  bFunctionTest = true;

//文件服务器参数
bool bServerConnectflag;
bool bServerConnectTest;
bool bServerActivedflag;

bool bSaveSdoPara = false;
bool bLoadSdoPara = false;

bool bModifyChannelPara = false;
bool bHadRegistSdo = false;


uint8 CurActiveProfId = 0;
str255 sActivePrfName;



//设备实际 可用参数 = 工厂参数和设置参数的交集
SDOSensorsParaEntryDesc SensorsAvalPara;

//设备出厂 参数 
SDOSensorsParaEntryDesc SensorsFactoryPara;

//设备设置 参数 
SDOSensorsParaEntryDesc SensorsSetPara;

SDOSensorsParaEntryDesc ChannelPeakVallyPara;

void InitAvaliableSdoArray(char* pName, SSensorsPara* psSysSensroPara, SDOSensorsParaEntryDesc* pEntryArrayPara)
{
	pEntryArrayPara->SensorsPara[0].Name = pName;
	pEntryArrayPara->SensorsPara[0].uSubIndex = 0;
	pEntryArrayPara->SensorsPara[0].Alias = "";
	pEntryArrayPara->SensorsPara[0].ObjAccess = ACCESS_RO;
	pEntryArrayPara->SensorsPara[0].SaveFlag = Save_None;
	pEntryArrayPara->SensorsPara[0].pVar = 0;
	pEntryArrayPara->SensorsPara[0].Maximum = 0;
	pEntryArrayPara->SensorsPara[0].Minimum = 0;
	pEntryArrayPara->SensorsPara[0].Desc = "";
	pEntryArrayPara->SensorsPara[0].ExtString = "";
	pEntryArrayPara->SensorsPara[0].BitOffset = 0;
	pEntryArrayPara->SensorsPara[0].BitLength = 0;

	pEntryArrayPara->SensorsPara[1].Name = "Sensors Actived Count";
	pEntryArrayPara->SensorsPara[1].uSubIndex = 1;
	pEntryArrayPara->SensorsPara[1].Alias = "";
	pEntryArrayPara->SensorsPara[1].ObjAccess = ACCESS_RO;

	pEntryArrayPara->SensorsPara[1].DataType = DT_U8;
	pEntryArrayPara->SensorsPara[1].pVar = &psSysSensroPara->SenActivedCount;
	pEntryArrayPara->SensorsPara[1].Maximum = 255;
	pEntryArrayPara->SensorsPara[1].Minimum = 0;
	pEntryArrayPara->SensorsPara[1].Desc = "";
	pEntryArrayPara->SensorsPara[1].ExtString = "";
	pEntryArrayPara->SensorsPara[1].BitOffset = 0;
	pEntryArrayPara->SensorsPara[1].BitLength = 0;
	pEntryArrayPara->SensorsPara[1].SaveFlag = Save_None;

	uint index = 1;
	pEntryArrayPara->iSensorsParaCount = 2;
	for (uint i = 0; i < psSysSensroPara->SenActivedCount; i++)
	{
		for (uint j = 0; j < 6; j++)
		{
			index++;
			pEntryArrayPara->iSensorsParaCount++;
			pEntryArrayPara->SensorsPara[index].uSubIndex = index;

			if (j % 6 == 0)
			{
				pEntryArrayPara->SensorsPara[index].Name = "Sensro Name";
				pEntryArrayPara->SensorsPara[index].DataType = DT_Str;
				pEntryArrayPara->SensorsPara[index].pVar = psSysSensroPara->sSensorUnit[i].spName;
				pEntryArrayPara->SensorsPara[index].ObjAccess = ACCESS_RO;
				pEntryArrayPara->SensorsPara[index].ExtString = "";
			}
			else if (j % 6 == 1)
			{
				pEntryArrayPara->SensorsPara[index].Name = "Sensro Type";
				pEntryArrayPara->SensorsPara[index].DataType = DT_I16;
				pEntryArrayPara->SensorsPara[index].pVar = &psSysSensroPara->sSensorUnit[i].eSensorType;
				pEntryArrayPara->SensorsPara[index].ObjAccess = ACCESS_RO;
				pEntryArrayPara->SensorsPara[index].ExtString = "EnumList#1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD";
			}
			else if (j % 6 == 2)
			{
				pEntryArrayPara->SensorsPara[index].Name = "Sensro Active";
				pEntryArrayPara->SensorsPara[index].DataType = DT_Bool;
				pEntryArrayPara->SensorsPara[index].pVar = &psSysSensroPara->sSensorUnit[i].bSensorActive;
				pEntryArrayPara->SensorsPara[index].ObjAccess = ACCESS_RO;
				pEntryArrayPara->SensorsPara[index].ExtString = "";
			}
			else if (j % 6 == 3)
			{
				pEntryArrayPara->SensorsPara[index].Name = (char*)malloc(strlen((char*)psSysSensroPara->sSensorUnit[i].spName) + 1);
				pEntryArrayPara->SensorsPara[index].Name = (char*)&psSysSensroPara->sSensorUnit[i].spName;
				pEntryArrayPara->SensorsPara[index].DataType = DT_F32;
				pEntryArrayPara->SensorsPara[index].pVar = psSysSensroPara->sSensorUnit[i].pfSensorVar;
				pEntryArrayPara->SensorsPara[index].ObjAccess = ACCESS_RO;
				pEntryArrayPara->SensorsPara[index].ExtString = "";
			}
			else if (j % 6 == 4)
			{
				pEntryArrayPara->SensorsPara[index].Name = "Sensro Maxinum";
				pEntryArrayPara->SensorsPara[index].DataType = DT_F32;

				pEntryArrayPara->SensorsPara[index].pVar = &psSysSensroPara->sSensorUnit[i].sLimRange.Maxinum;
				pEntryArrayPara->SensorsPara[index].ObjAccess = ACCESS_RO;
				pEntryArrayPara->SensorsPara[index].Minimum = 0;
				pEntryArrayPara->SensorsPara[index].Maximum = 1000000;
				pEntryArrayPara->SensorsPara[index].ExtString = "";
			}
			else if (j % 6 == 5)
			{
				pEntryArrayPara->SensorsPara[index].Name = "Sensro Mininum";
				pEntryArrayPara->SensorsPara[index].DataType = DT_F32;
				pEntryArrayPara->SensorsPara[index].pVar = &psSysSensroPara->sSensorUnit[i].sLimRange.Mininum;
				pEntryArrayPara->SensorsPara[index].ObjAccess = ACCESS_RO;

				pEntryArrayPara->SensorsPara[index].Minimum = -100000;
				pEntryArrayPara->SensorsPara[index].Maximum = 0;
				pEntryArrayPara->SensorsPara[index].ExtString = "";
			}

			pEntryArrayPara->SensorsPara[index].SaveFlag = Save_None;

			pEntryArrayPara->SensorsPara[index].Alias = "";
			pEntryArrayPara->SensorsPara[index].Desc = "";

			pEntryArrayPara->SensorsPara[index].BitOffset = 0;
			pEntryArrayPara->SensorsPara[index].BitLength = 0;
		}
	}
}

void InitSdoArray(char* pName, SSensorsPara* psSysSensroPara, SDOSensorsParaEntryDesc* pEntryArrayPara)
{
	pEntryArrayPara->SensorsPara[0].Name = pName;
	pEntryArrayPara->SensorsPara[0].uSubIndex = 0;
	pEntryArrayPara->SensorsPara[0].Alias = "";
	pEntryArrayPara->SensorsPara[0].ObjAccess = ACCESS_RO;
	pEntryArrayPara->SensorsPara[0].SaveFlag = Save_None;
	pEntryArrayPara->SensorsPara[0].pVar = 0;
	pEntryArrayPara->SensorsPara[0].Maximum = 0;
	pEntryArrayPara->SensorsPara[0].Minimum = 0;
	pEntryArrayPara->SensorsPara[0].Desc = "";
	pEntryArrayPara->SensorsPara[0].ExtString = "";
	pEntryArrayPara->SensorsPara[0].BitOffset = 0;
	pEntryArrayPara->SensorsPara[0].BitLength = 0;

	pEntryArrayPara->SensorsPara[1].Name = "Sensors Actived Count";
	pEntryArrayPara->SensorsPara[1].uSubIndex = 1;
	pEntryArrayPara->SensorsPara[1].Alias = "";


	if (strcmp(pEntryArrayPara->SensorsPara[0].Name, "Sensors Facory Para") == 0)
	{
		pEntryArrayPara->SensorsPara[1].SaveFlag = Save_None;// Save_User;////Save_Factory;
	}
	else
	{
		pEntryArrayPara->SensorsPara[1].SaveFlag = Save_None;
	}

	pEntryArrayPara->SensorsPara[1].ObjAccess = ACCESS_RO;// ACCESS_RW;

	pEntryArrayPara->SensorsPara[1].DataType = DT_U8;
	pEntryArrayPara->SensorsPara[1].pVar = &psSysSensroPara->SenActivedCount;

	pEntryArrayPara->SensorsPara[1].Maximum = 0;
	pEntryArrayPara->SensorsPara[1].Minimum = 0;
	pEntryArrayPara->SensorsPara[1].Desc = "";
	pEntryArrayPara->SensorsPara[1].ExtString = "";
	pEntryArrayPara->SensorsPara[1].BitOffset = 0;
	pEntryArrayPara->SensorsPara[1].BitLength = 0;

	pEntryArrayPara->iSensorsParaCount = psSysSensroPara->SenActivedCount;//2;

	uint index = 1;
	if (psSysSensroPara->SenActivedCount > 0)
	{
		for (uint i = 0; i < psSysSensroPara->SenActivedCount; i++)
		{
			for (uint j = 0; j < 4; j++)
			{
				index++;
				pEntryArrayPara->iSensorsParaCount++;
				pEntryArrayPara->SensorsPara[index].uSubIndex = index;

				if (j % 4 == 0)
				{
					pEntryArrayPara->SensorsPara[index].Name = "Sensor Name";
					pEntryArrayPara->SensorsPara[index].DataType = DT_Str;
					pEntryArrayPara->SensorsPara[index].pVar = psSysSensroPara->sSensorUnit[i].spName;
					pEntryArrayPara->SensorsPara[index].ObjAccess = ACCESS_RW;
					pEntryArrayPara->SensorsPara[index].ExtString = "";
				}
				else if (j % 4 == 1)
				{
					pEntryArrayPara->SensorsPara[index].Name = "Sensro Type";
					pEntryArrayPara->SensorsPara[index].DataType = DT_I16;
					pEntryArrayPara->SensorsPara[index].pVar = &psSysSensroPara->sSensorUnit[i].eSensorType;
					pEntryArrayPara->SensorsPara[index].ObjAccess = ACCESS_RO;

					// sscanf(,"%s", sSensroType);
					pEntryArrayPara->SensorsPara[index].ExtString = "EnumList#1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD";
				}
				else if (j % 4 == 2)
				{
					pEntryArrayPara->SensorsPara[index].Name = "Sensor Maxinum";
					pEntryArrayPara->SensorsPara[index].DataType = DT_F32;
					pEntryArrayPara->SensorsPara[index].pVar = &psSysSensroPara->sSensorUnit[i].sLimRange.Maxinum;

					if (strcmp(pEntryArrayPara->SensorsPara[0].Name, "Sensors Facory Para") == 0)
					{
						pEntryArrayPara->SensorsPara[index].ObjAccess = ACCESS_RW;
					}
					else
					{
						pEntryArrayPara->SensorsPara[index].ObjAccess = ACCESS_RO;
					}

					pEntryArrayPara->SensorsPara[index].Minimum = 0;
					pEntryArrayPara->SensorsPara[index].Maximum = 1000000;
					pEntryArrayPara->SensorsPara[index].ExtString = "";
				}
				else if (j % 4 == 3)
				{
					pEntryArrayPara->SensorsPara[index].Name = "Sensor Mininum";
					pEntryArrayPara->SensorsPara[index].DataType = DT_F32;
					pEntryArrayPara->SensorsPara[index].pVar = &psSysSensroPara->sSensorUnit[i].sLimRange.Mininum;

					if (strcmp(pEntryArrayPara->SensorsPara[0].Name, "Sensors Facory Para") == 0)
					{
						pEntryArrayPara->SensorsPara[index].ObjAccess = ACCESS_RW;
					}
					else
					{
						pEntryArrayPara->SensorsPara[index].ObjAccess = ACCESS_RO;
					}

					pEntryArrayPara->SensorsPara[index].Minimum = -100000;
					pEntryArrayPara->SensorsPara[index].Maximum = 0;
					pEntryArrayPara->SensorsPara[index].ExtString = "";
				}

				if (strcmp(pEntryArrayPara->SensorsPara[0].Name, "Sensors Facory Para") == 0)
					pEntryArrayPara->SensorsPara[index].SaveFlag = Save_User;//Save_Factory;
				else
					pEntryArrayPara->SensorsPara[index].SaveFlag = Save_None;

				pEntryArrayPara->SensorsPara[index].Alias = "";
				pEntryArrayPara->SensorsPara[index].Desc = "";

				pEntryArrayPara->SensorsPara[index].BitOffset = 0;
				pEntryArrayPara->SensorsPara[index].BitLength = 0;
			}
		}
	}
}

uint8 uChannelCount = 0;
void InitChannelPeakVallyPara(SSensorsPara* psSysSensroPara, SDOSensorsParaEntryDesc* pEntryArrayPara)
{
	pEntryArrayPara->SensorsPara[0].Name = "Channel Peak Vally Data";
	pEntryArrayPara->SensorsPara[0].uSubIndex = 0;
	pEntryArrayPara->SensorsPara[0].Alias = "";
	pEntryArrayPara->SensorsPara[0].ObjAccess = ACCESS_RO;
	pEntryArrayPara->SensorsPara[0].SaveFlag = Save_None;
	pEntryArrayPara->SensorsPara[0].pVar = 0;
	pEntryArrayPara->SensorsPara[0].Maximum = 0;
	pEntryArrayPara->SensorsPara[0].Minimum = 0;
	pEntryArrayPara->SensorsPara[0].Desc = "";
	pEntryArrayPara->SensorsPara[0].ExtString = "";
	pEntryArrayPara->SensorsPara[0].BitOffset = 0;
	pEntryArrayPara->SensorsPara[0].BitLength = 0;

	pEntryArrayPara->SensorsPara[1].Name = "Channel Count";
	pEntryArrayPara->SensorsPara[1].uSubIndex = 1;
	pEntryArrayPara->SensorsPara[1].Alias = "";
	pEntryArrayPara->SensorsPara[1].ObjAccess = ACCESS_RO;
	pEntryArrayPara->SensorsPara[1].SaveFlag = Save_None;       //Save_Factory;
	pEntryArrayPara->SensorsPara[1].DataType = DT_U8;

	if (SYSTEM_MOTION_MODE)
	{
		if (SYSTEM_MOVE_SERIES == 0)
		{
			uChannelCount = 2;
		}
		else if (SYSTEM_MOVE_SERIES == 1)
			uChannelCount = 3;       //去掉 V  、Acc、Current、Torque
	}
	else
	{
		uChannelCount = psSysSensroPara->SenActivedCount;       //去掉 V  、Acc、Current、Torque
	}
	pEntryArrayPara->SensorsPara[1].pVar = &uChannelCount;
	pEntryArrayPara->SensorsPara[1].Maximum = 0;
	pEntryArrayPara->SensorsPara[1].Minimum = 0;
	pEntryArrayPara->SensorsPara[1].Desc = "";
	pEntryArrayPara->SensorsPara[1].ExtString = "";
	pEntryArrayPara->SensorsPara[1].BitOffset = 0;
	pEntryArrayPara->SensorsPara[1].BitLength = 0;
	pEntryArrayPara->iSensorsParaCount = 2;

	uint8 index = 1;
	uint8 uChannelIndex = 0;
	if (uChannelCount > 0)
	{
		for (uint8 i = 0; i < uChannelCount; i++)
		{
			for (uint8 j = 0; j < 4; j++)
			{
				index++;

				if (SYSTEM_MOTION_MODE)
				{
					if (i == 0)
						uChannelIndex = i;
					else
					{
						uChannelIndex = i + 4;  //去掉 V  、Acc、Current、Torque
					}
				}
				else
				{
					uChannelIndex = i + 5;      //监控仪模式使用通道5、6
				}
				pEntryArrayPara->SensorsPara[index].uSubIndex = index;
				pEntryArrayPara->SensorsPara[index].ExtString = "";
				pEntryArrayPara->SensorsPara[index].Desc = "";
				pEntryArrayPara->SensorsPara[index].BitOffset = 0;
				pEntryArrayPara->SensorsPara[index].BitLength = 0;
				pEntryArrayPara->SensorsPara[index].ObjAccess = ACCESS_RO;
				pEntryArrayPara->SensorsPara[index].SaveFlag = Save_None;////Save_Factory;
				pEntryArrayPara->SensorsPara[index].Alias = "";
				pEntryArrayPara->SensorsPara[index].Minimum = 0;
				pEntryArrayPara->SensorsPara[index].Maximum = 0;
				if (j % 4 == 0)
				{
					pEntryArrayPara->SensorsPara[index].Name = "Channel Name";
					pEntryArrayPara->SensorsPara[index].DataType = DT_Str;
					pEntryArrayPara->SensorsPara[index].pVar = psSysSensroPara->sSensorUnit[uChannelIndex].spName;
				}
				else if (j % 4 == 1)
				{
					pEntryArrayPara->SensorsPara[index].Name = "Channel Type";
					pEntryArrayPara->SensorsPara[index].DataType = DT_I16;
					pEntryArrayPara->SensorsPara[index].pVar = &psSysSensroPara->sSensorUnit[uChannelIndex].eSensorType;

					pEntryArrayPara->SensorsPara[index].ExtString = "EnumList#1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD";
				}
				else if (j % 4 == 2)
				{
					pEntryArrayPara->SensorsPara[index].Name = "Channel PeakVar";
					pEntryArrayPara->SensorsPara[index].DataType = DT_F32;
					pEntryArrayPara->SensorsPara[index].pVar = &psSysSensroPara->sSensorUnit[uChannelIndex].sRunSeqPeakValleyData.fPeakVal;
					pEntryArrayPara->SensorsPara[index].Minimum = 0;
					pEntryArrayPara->SensorsPara[index].Maximum = 1000000;
				}
				else if (j % 4 == 3)
				{
					pEntryArrayPara->SensorsPara[index].Name = "Channel Vally Var";
					pEntryArrayPara->SensorsPara[index].DataType = DT_F32;
					pEntryArrayPara->SensorsPara[index].pVar = &psSysSensroPara->sSensorUnit[uChannelIndex].sRunSeqPeakValleyData.fValleyVal;
					pEntryArrayPara->SensorsPara[index].Minimum = -100000;
					pEntryArrayPara->SensorsPara[index].Maximum = 0;
				}
				pEntryArrayPara->iSensorsParaCount++;
			}
		}
	}
}

void InitSensosEntryPara()
{
	//设备实际 可用参数 = 工厂参数和设置参数的交集
	if (SYSTEM_MOTION_MODE)
		InitSdoArray("Sensors Facory Para", &sSysFactoryLimitPara, &SensorsFactoryPara);

	//设备出厂 参数      0x5001组sdo
	InitSdoArray("Sensors Set Para", &sSysSetLimitPara, &SensorsSetPara);

	//设备出厂 可用       0x5000组sdo
	InitAvaliableSdoArray("Sensors Avaliable Para", &pSysShareData->sSysAvaliablePara, &SensorsAvalPara);

	InitChannelPeakVallyPara(&pSysShareData->sSysAvaliablePara, &ChannelPeakVallyPara);
	for (uint8 i = 0; i < arraySize(entryGroup); i++)
	{
		if (entryGroup[i].uIndex == 0x4001)
		{
			entryGroup[i].uSdoEntryCount = ChannelPeakVallyPara.iSensorsParaCount;
			printf("0x4001 uSdoEntryCount:%d\n", ChannelPeakVallyPara.iSensorsParaCount);
		}
		else if (entryGroup[i].uIndex == 0x5000)     //Avaliable
		{
			entryGroup[i].uSdoEntryCount = SensorsAvalPara.iSensorsParaCount;
			printf("0x5000 uSdoEntryCount:%d\n", SensorsAvalPara.iSensorsParaCount);

		}
		else if (entryGroup[i].uIndex == 0x5001 && SYSTEM_MOTION_MODE)    //Facory
		{
			entryGroup[i].uSdoEntryCount = SensorsFactoryPara.iSensorsParaCount;
			printf("0x5001 uSdoEntryCount:%d\n", SensorsFactoryPara.iSensorsParaCount);

		}
		else if (entryGroup[i].uIndex == 0x5002)    //Set
		{
			entryGroup[i].uSdoEntryCount = SensorsSetPara.iSensorsParaCount;
			printf("0x5002 uSdoEntryCount:%d\n", SensorsSetPara.iSensorsParaCount);
			break;
		}
	}
}

SDOEntryDesc entryEoResult[51];

void InitEoResultSdo()
{
	str16 sTmpName;
	entryEoResult[0] = { 0x0,	"Eo Result",              "",     DT_None,    ACCESS_RO,  Save_None,  0,                                                          0,      0,      "","",0,0 };
	for (uint i = 1; i <= 50; i++)
	{
		entryEoResult[i].uSubIndex = i;
		sprintf(sTmpName, "Eo Result %d", i);

		//if (entryEoResult[i].Name)
		//    free(entryEoResult[i].Name);
		entryEoResult[i].Name = (char*)malloc(strlen(sTmpName) + 1);
		strcpy(entryEoResult[i].Name, sTmpName);

		entryEoResult[i].Alias = "";
		entryEoResult[i].DataType = DT_F32;                 //EDataType DataType;
		entryEoResult[i].ObjAccess = ACCESS_RO;             //EAccessType ObjAccess;
		entryEoResult[i].SaveFlag = Save_None;              //ESaveFlag SaveFlag;
		entryEoResult[i].pVar = (float32*)&aEoResult[i - 1];//void* pVar;
		entryEoResult[i].Minimum = -3.402823e38;// FLT_MIN;
		entryEoResult[i].Maximum = FLT_MAX;
		entryEoResult[i].Desc = "";                         //char* Desc;
		entryEoResult[i].ExtString = "";                    //char* ExtString;
		entryEoResult[i].BitOffset = 0;                     //uint8	BitOffset;
		entryEoResult[i].BitLength = 0;                     //uint8	BitLength;
	}
}

SDOEntryDesc entryEoPosition[51];

void InitEoPositionSdo()
{
	str16 sTmpName;
	entryEoPosition[0] = { 0x0,	"Eo Position Para",              "",     DT_None,    ACCESS_RO,  Save_None,  0,                                                          0,      0,      "","",0,0 };
	for (uint i = 1; i <= 50; i++)
	{
		entryEoPosition[i].uSubIndex = i;
		sprintf(sTmpName, "Eo Position %d", i);

		entryEoPosition[i].Name = (char*)malloc(strlen(sTmpName) + 1);
		strcpy(entryEoPosition[i].Name, sTmpName);

		entryEoPosition[i].Alias = "";
		entryEoPosition[i].DataType = DT_F32;                       //EDataType DataType;
		entryEoPosition[i].ObjAccess = ACCESS_RW;                   //EAccessType ObjAccess;
		entryEoPosition[i].SaveFlag = Save_None;                    //ESaveFlag SaveFlag;
		entryEoPosition[i].pVar = (float32*)&aEoPosition[i - 1];    //void* pVar;
		entryEoPosition[i].Minimum = -3.402823e38;// FLT_MIN;// -10000.0;
		entryEoPosition[i].Maximum = FLT_MAX;// 10000.0;
		entryEoPosition[i].Desc = "";                               //char* Desc;
		entryEoPosition[i].ExtString = "";                          //char* ExtString;
		entryEoPosition[i].BitOffset = 0;                           //uint8	BitOffset;
		entryEoPosition[i].BitLength = 0;                           //uint8	BitLength;
	}
}

void InitGlobalVarSdo()
{
	entryGlobalVar[0] = { 0x0,	"GlobalVar",              "",     DT_None,    ACCESS_RO,  Save_None,  0,                                                          0,      0,      "","",0,0 };
	for (uint i = 1; i <= 50; i++)
	{
		entryGlobalVar[i].uSubIndex = i;
		entryGlobalVar[i].Name = (char*)&sSysGlobalVar.aSVariableVar[i].sName;
		entryGlobalVar[i].DataType = sSysGlobalVar.aSVariableVar[i].eDataType;
		entryGlobalVar[i].Alias = "";
		entryGlobalVar[i].ObjAccess = ACCESS_RW;                        //EAccessType ObjAccess;
		entryGlobalVar[i].SaveFlag = Save_None;                         //ESaveFlag SaveFlag;
		entryGlobalVar[i].Desc = "";                                    //char* Desc;
		entryGlobalVar[i].ExtString = "";                               //char* ExtString;
		entryGlobalVar[i].BitOffset = 0;                                //uint8	BitOffset;
		entryGlobalVar[i].BitLength = 0;                                //uint8	BitLength;
		entryGlobalVar[i].pVar = sSysGlobalVar.aSVariableVar[i].pVar;   //sSysGlobalVar
	}
}

void InitLocalVarSdo()
{
	entryLocalVar[0] = { 0x0,	"LocalVar",              "",     DT_None,    ACCESS_RO,  Save_None,  0,                                                          0,      0,      "","",0,0 };
	for (uint i = 1; i <= 50; i++)
	{
		entryLocalVar[i].uSubIndex = i;
		entryLocalVar[i].DataType = DT_None;
		entryLocalVar[i].Alias = "";
		entryLocalVar[i].ObjAccess = ACCESS_RW;                   //EAccessType ObjAccess;
		entryLocalVar[i].SaveFlag = Save_None;                    //ESaveFlag SaveFlag;
		entryLocalVar[i].Desc = "";                               //char* Desc;
		entryLocalVar[i].ExtString = "";                          //char* ExtString;
		entryLocalVar[i].BitOffset = 0;                           //uint8	BitOffset;
		entryLocalVar[i].BitLength = 0;                           //uint8	BitLength;
		entryLocalVar[i].pVar = sLocalVar.aSVariableVar[i].pVar;
	}
}


SDOEntryDesc entry1000[12] =
{
	/*************** Key  **  Name  **********************, Alias* DataType * ObjAccess *********************  pVar  ****************************  Minimum  ****  Maximum ***************************************************************************/
	entry1000[0] = { 0x0,   "Information",                  "",     DT_None,    ACCESS_RO,   Save_None,                                     0,           0,         0,      "",          "",                                      0,             0 },
	entry1000[1] = { 0x1,	"Display Software Version",	    "",     DT_Str,     ACCESS_RO,   Save_None, (void*)SysInfo.DisplaySoftwareVersion,           0,         0,      "",          "",                                      0,             0 },
	entry1000[3] = { 0x3,	"Hardware   Version",		    "",     DT_Str,     ACCESS_RO,   Save_None, (void*)SysInfo.HardwareVersion, 	             0,         0,      "",          "",                                      0,             0 },
	entry1000[2] = { 0x2,	"Control    Version",		    "",     DT_Str,     ACCESS_RO,   Save_None, (void*)SysInfo.ControlSoftwareVersion,           0,         0,      "",          "",                                      0,             0 },
	entry1000[4] = { 0x4,	"Fieldbus   Version",			"",     DT_Str,     ACCESS_RO,   Save_None, (void*)SysInfo.FieldbusVersion, 	             0,         0,      "",          "",                                      0,             0 },
	entry1000[5] = { 0x5,	"Profile    Version",			"",     DT_Str,     ACCESS_RO,   Save_None, (void*)SysInfo.ProfileVersion, 	                 0,         0,      "",          "",                                      0,             0 },
	entry1000[6] = { 0x6,	"ResultFile Version",		    "",     DT_Str,     ACCESS_RO,   Save_None, (void*)SysInfo.ResultFileVersion,                0,         0,      "",          "",                                      0,             0 },
	entry1000[7] = { 0x7,	"SqliteOp   Version",			"",     DT_Str,     ACCESS_RO,   Save_None, (void*)SysInfo.sSqliteOpVersion, 	             0,         0,      "",          "",                                      0,             0 },
	entry1000[8] = { 0x8,	"SqliteWeb  Version",		    "",     DT_Str,     ACCESS_RO,   Save_None, (void*)SysInfo.sSqliteWebVersion,                0,         0,      "",          "",                                      0,             0 },

	entry1000[9] = { 0xA0,	"System  Mode",		            "",     DT_U8,      ACCESS_RO,   Save_None,  &uSystemMode,                                   0,         1,      "",          "EnumList#0:Measure;1:Motion Controller",0,             0 },
	entry1000[10] = { 0xB0,	"Move    Mode",		            "",     DT_U8,      ACCESS_RO,   Save_None,  &uEcoMoveMode,                                  0,         1,      "",          "EnumList#0:EcoMove;1:AccMove",          0,             0 },
	entry1000[11] = { 0xC0,	"Curve    Mode",		        "",     DT_U8,      ACCESS_RW,   Save_User,  &uCurveMode,                                    0,         1,      "",          "EnumList#0:NoneCurve;1:SendCurve",      0,             0 }
};

SDOEntryDesc entry1100[29] =
{
	/*************** Key  **  Name  ********************, Alias* DataType * ObjAccess ****  pVar  ******************************************************  Minimum  ******  Maximum ***/
	entry1100[0] = { 0x0,	"MainThreadInfo",               "",     DT_None,    ACCESS_RO,   Save_None,  0,                                                0,              0,      "","",0,0 },
	entry1100[1] = { 0x1,   "SystemCountor",                "",     DT_U64,     ACCESS_RO,   Save_None,  &pSysShareData->gSysCounter,                      0,              0,      "", "" },
	entry1100[2] = { 0x2,   "ExecuteTime",                  "",     DT_F64,     ACCESS_RO,   Save_None,  &pSysShareData->rt_ThreadExecInfo.ExecTime_uS,    0,              0,      "", "", 0, 0 },
	entry1100[3] = { 0x3,   "MaxExecuteTime",               "",     DT_F64,     ACCESS_RO,   Save_None,  &pSysShareData->rt_ThreadExecInfo.MaxExecTime_uS, 0,              0,      "", "", 0, 0 },
	entry1100[4] = { 0x4,   "MinExecuteTime",               "",     DT_F64,     ACCESS_RO,   Save_None,  &pSysShareData->rt_ThreadExecInfo.MinExecTime_uS, 0,              0,      "", "", 0, 0 },
	entry1100[5] = { 0x5,   "Jitter",                       "",     DT_F64,     ACCESS_RO,   Save_None,  &pSysShareData->rt_ThreadExecInfo.Jitter_uS,      0,              0,      "", "", 0, 0 },
	entry1100[6] = { 0x6,   "MaxJitter",                    "",     DT_F64,     ACCESS_RO,   Save_None,  &pSysShareData->rt_ThreadExecInfo.MaxJitter_uS,   0,              0,      "", "", 0, 0 },
	entry1100[7] = { 0x7,	"MinJitter",                    "",     DT_F64,     ACCESS_RO,   Save_None,  &pSysShareData->rt_ThreadExecInfo.MinJitter_uS,   0,              0,      "","",0,0 },
	entry1100[8] = { 0x8,	"MinMax_Reset",                 "",     DT_Bool,    ACCESS_RW,   Save_None,  &pSysShareData->rt_ThreadExecInfo.bResetFlag,     0,              0,      "", "SendUpEdge#10" },

	entry1100[9] = { 0x10,	"CPULoad",                      "",     DT_F32,     ACCESS_RO,   Save_None,  &pSysShareData->rt_ThreadExecInfo.CPULoad,        0,              0,      "","",0,0 },
	entry1100[10] = { 0x11,	"MemoryLoad",                   "",     DT_F32,     ACCESS_RO,   Save_None,  &pSysShareData->rt_ThreadExecInfo.MemoryLoad,     0,              0,      "","",0,0 },
	entry1100[11] = { 0x12,	"NetworkLoad",                  "",     DT_F32,     ACCESS_RO,   Save_None,  &pSysShareData->rt_ThreadExecInfo.NetworkLoad,    0,              0,      "","",0,0 },
	entry1100[12] = { 0x40,	"Connect Heart beat",           "",     DT_Bool,     ACCESS_RO,  Save_None,  &bHeartbeat,                                      0,              0,      "","",0,0 },
	entry1100[13] = { 0x50,	"NextPeriod_uS",                "",     DT_F64,     ACCESS_RO,   Save_None,  &pSysShareData->rt_ThreadExecInfo.NextPeriod_uS,  0,              0,      "", "",0,0 },
	entry1100[14] = { 0x51,	"WaitTime_uS",                  "",     DT_F64,     ACCESS_RO,   Save_None,  &pSysShareData->rt_ThreadExecInfo.WaitTime_uS,    0,              0,      "", "",0,0 },
	entry1100[15] = { 0x52,	"RunPeriod_uS",                  "",     DT_I32,     ACCESS_RO,   Save_None,  &pSysShareData->rt_ThreadExecInfo.RunPeriod_uS,   0,              0,      "", "",0,0 },


	entry1100[16] = { 0x60, "EtherCat ExecuteTime",         "",     DT_F64,     ACCESS_RO,   Save_None,  &pSysShareData->EtherCat_ThreadExecInfo.ExecTime_uS,    0,              0,      "", "", 0, 0 },
	entry1100[17] = { 0x61, "MaxExecuteTime",               "",     DT_F64,     ACCESS_RO,   Save_None,  &pSysShareData->EtherCat_ThreadExecInfo.MaxExecTime_uS, 0,              0,      "", "", 0, 0 },
	entry1100[18] = { 0x62, "MinExecuteTime",               "",     DT_F64,     ACCESS_RO,   Save_None,  &pSysShareData->EtherCat_ThreadExecInfo.MinExecTime_uS, 0,              0,      "", "", 0, 0 },
	entry1100[19] = { 0x63, "Jitter",                       "",     DT_F64,     ACCESS_RO,   Save_None,  &pSysShareData->EtherCat_ThreadExecInfo.Jitter_uS,      0,              0,      "", "", 0, 0 },
	entry1100[20] = { 0x64, "MaxJitter",                    "",     DT_F64,     ACCESS_RO,   Save_None,  &pSysShareData->EtherCat_ThreadExecInfo.MaxJitter_uS,   0,              0,      "", "", 0, 0 },
	entry1100[21] = { 0x65,	"MinJitter",                    "",     DT_F64,     ACCESS_RO,   Save_None,  &pSysShareData->EtherCat_ThreadExecInfo.MinJitter_uS,   0,              0,      "","",0,0 },
	entry1100[22] = { 0x66,	"MinMax_Reset",                 "",     DT_Bool,    ACCESS_RW,   Save_None,  &pSysShareData->EtherCat_ThreadExecInfo.bResetFlag,     0,              0,      "", "SendUpEdge#10" },

	entry1100[23] = { 0x67,	"CPULoad",                      "",     DT_F32,     ACCESS_RO,   Save_None,  &pSysShareData->EtherCat_ThreadExecInfo.CPULoad,        0,              0,      "","",0,0 },
	entry1100[24] = { 0x68,	"MemoryLoad",                   "",     DT_F32,     ACCESS_RO,   Save_None,  &pSysShareData->EtherCat_ThreadExecInfo.MemoryLoad,     0,              0,      "","",0,0 },
	entry1100[25] = { 0x69,	"NetworkLoad",                  "",     DT_F32,     ACCESS_RO,   Save_None,  &pSysShareData->EtherCat_ThreadExecInfo.NetworkLoad,    0,              0,      "","",0,0 },
	entry1100[26] = { 0x6A,	"NextPeriod_uS",                "",     DT_F64,     ACCESS_RO,   Save_None,  &pSysShareData->EtherCat_ThreadExecInfo.NextPeriod_uS,  0,              0,      "", "",0,0 },
	entry1100[27] = { 0x6B,	"WaitTime_uS",                  "",     DT_F64,     ACCESS_RO,   Save_None,  &pSysShareData->EtherCat_ThreadExecInfo.WaitTime_uS,    0,              0,      "", "",0,0 },
	entry1100[28] = { 0x6C,	"RunPeriod_uS",                 "",     DT_I32,     ACCESS_RO,   Save_None,  &pSysShareData->EtherCat_ThreadExecInfo.RunPeriod_uS,    0,              0,      "", "",0,0 },
};

SDOEntryDesc entry1200[11] =
{
	/*************** Key  **  Name  *******************, Alias* DataType * ObjAccess ******************************  pVar  ******************************  Minimum  ******  Maximum ***/
	entry1200[0] = { 0x0,	"SystemSet",					"",    DT_None,     ACCESS_RO,   Save_None,  0,              				      0,              0,      "","",0,0 },
	entry1200[1] = { 0x1,	"Devive Name",                  "",    DT_Str,      ACCESS_RW,   Save_User,  &sSysDeviceSetInfo.sDeviveName ,     0,              0,      "","",0,0 },
	entry1200[2] = { 0x2,	"Device GroupIndex",            "",    DT_U16,      ACCESS_RW,   Save_User,  &sSysDeviceSetInfo.uDevGroupIndex ,  0,              0xFFFF, "","",0,0 },
	entry1200[3] = { 0x3,	"Login User Name",              "",    DT_Str,      ACCESS_RW,   Save_None,  &sSysDeviceSetInfo.LoginUserName ,   0,              0,      "","",0,0 },
	entry1200[4] = { 0x10,	"Control Ip",                   "",    DT_Str,      ACCESS_RW,	 Save_User,  &sSysDeviceSetInfo.ControlIp,        0,              0,      "","",0,0 },
	entry1200[5] = { 0x11,	"Display Ip",                   "",    DT_Str,      ACCESS_RW,	 Save_User,  &sSysDeviceSetInfo.DisplayIp,        0,              0,      "","",0,0 },
	entry1200[6] = { 0x20,	"Current Time&Time",            "",    DT_Str,      ACCESS_RO,	 Save_User,  &sSysDeviceSetInfo.DispTime,         0,              0,      "","",0,0 },
	entry1200[7] = { 0x21,	"Set     Time&Time",            "",    DT_Str,      ACCESS_RW,	 Save_User,  &sSysDeviceSetInfo.SetTime,          0,              0,      "","",0,0 },
	entry1200[8] = { 0x22,	"bUpdate Time&Time",            "",    DT_Bool,     ACCESS_RW,	 Save_User,  &sSysDeviceSetInfo.bUpdateTime,      0,              1,      "","SendUpEdge#10",0,0 },
	entry1200[9] = { 0x23,	"Had Update Time",              "",    DT_Bool,     ACCESS_RO,	 Save_User,  &sSysDeviceSetInfo.bHadUpdateProtectTime,            0,              0,      "","",0,0 },
	entry1200[10] = { 0x24,	"Protect Time",                 "",    DT_Str,      ACCESS_RO,	 Save_User,  &sSysDeviceSetInfo.sProtectTime,     0,              0,      "","",0,0 },
};

SDOEntryDesc entry1A00[4]
{
	/***************  Key  **  Name  *********************, Alias* DataType * ObjAccess ************** pVar  ********************************** Minimum *** Maximum ** ExtString ** BitOffset ** BitLength ***** */
	entry1A00[0] = { 0x0,	"Display Control Command",      "",     DT_None,    ACCESS_RO,   Save_None,  0,                                         0,          0,      "","",         0,             0 }, //客户端控制命令
	entry1A00[1] = { 0x1,	"Save Sdo Command",             "",     DT_Bool,    ACCESS_RW,   Save_None,  &pSysShareData->sExCW.bSaveSdoConfig,      0,          1,      "","",         0,             1 }, //保存Sdo
	entry1A00[2] = { 0x2,	"Save Sdo Counter",             "",     DT_U64,     ACCESS_RO,   Save_None,  &lSdoSaveCounter,                          0, 1000000000,      "","",         0,             1 }, //保存Sdo
	entry1A00[3] = { 0x3,	"Profile Modify Counter",       "",     DT_I16,     ACCESS_RO,   Save_None,  &sSysApiCounter.profile,              -32768,      32767,      "","",         0,             1 } //保存Sdo};
};

SDOEntryDesc entry1F00[14] =
{
	/*************** Key  **  Name  *********************, Alias* DataType * ObjAccess ************** pVar  ****************************************************** Minimum *** Maximum ** ExtString ** BitOffset ** BitLength ***** */
	entry1F00[0] = { 0x00, "System Connect State",           "",     DT_None,     ACCESS_RO, Save_None,                              0,                              0,      0, "", "", 0, 0 }, //
	entry1F00[1] = { 0x01, "Fieldbus Connect",               "",     DT_Bool,     ACCESS_RO, Save_None, &pSysShareData->bFiledBusOk,                                 0,      1, "", "", 0, 1 },
	entry1F00[2] = { 0x02, "Fieldbus Type",                  "",     DT_U8,       ACCESS_RO, Save_None, &pSysShareData->eFieldbusType,                               0,    255, "", "EnumList#0:None;1:Profinet;2:Ethercat;3:Ethernet_IP;4:CC_LINK;5:DeviceNet;6:Modbus_TCP;7:Profibus;8:CANopen;9:CC_Link_IE_Field;10:Powerlink;11:BACnet_IP;12:CommonEthernet;255:UnknownType", 0, 0 }, //
	entry1F00[3] = { 0x03, "Fielsbus Remote",                "",     DT_Bool,     ACCESS_RO, Save_None, (uint8*)&pSysShareData->SCSW ,                               0,      1, "","",         0,             1 }, //  
	entry1F00[4] = { 0x10, "Explorer Connect",               "",     DT_Bool,     ACCESS_RO, Save_None, &bExplorerConnectet,                                         0,      1, "", "", 0, 1 }, //客户端是否连接（心跳）
	entry1F00[5] = { 0x20, "Data Server Actived",            "",     DT_Bool,     ACCESS_RO, Save_None, &bServerActivedflag,                                         0,      1, "", "", 0, 1 }, //文件服务器是否激活
	entry1F00[6] = { 0x21, "Data Server Connect",            "",     DT_Bool,     ACCESS_RO, Save_None, &bServerConnectflag,                                         0,      1, "", "", 0, 1 }, //文件服务器是否连接
	entry1F00[7] = { 0x30, "Control Type",                   "",     DT_U8,       ACCESS_RO, Save_None, &pSysShareData->sExSW.eControlType,                          0,      2, "", "EnumList#0:Explorer;1:IO;2:Fieldbus", 0, 1 }, //文件服务器是否连接

	entry1F00[8] = { 0x40, "HardWare Version",              "",     DT_Str,     ACCESS_RO, Save_None, &pSysShareData->sBusVersionInfo.ModuleHardWareVersion ,       0,      1, "", "", 0, 1 },
	entry1F00[9] = { 0x41, "Soft Version",                  "",     DT_Str,     ACCESS_RO, Save_None, &pSysShareData->sBusVersionInfo.ModuleSoftWareVersion,        0,      1, "", "", 0, 1 },
	entry1F00[10] = { 0x42, "Host Version",                  "",     DT_Str,     ACCESS_RO, Save_None, &pSysShareData->sBusVersionInfo.AB2SLibVersion_Host,          0,      1, "", "", 0, 1 },
	entry1F00[11] = { 0x43, "Slave Version",                 "",     DT_Str,     ACCESS_RO, Save_None, &pSysShareData->sBusVersionInfo.AB2SLibVersion_Slave,         0,      1, "", "", 0, 1 },
	entry1F00[12] = { 0x4F, "Active Fieldbus",               "",     DT_Bool,    ACCESS_RW, Save_User, &pSysShareData->bActiveFieldbus,                              0,      1, "", "", 0, 1 }
};

SDOEntryDesc entry2012[27] =
{
	/*Key  **  Name  *********************, Alias* DataType * ObjAccess ************** pVar  ****************** Minimum *** Maximum ** ExtString ** BitOffset ** BitLength ***** */
	entry2012[0] = { 0x0,	"Explorer Common Control Word", "",     DT_None,   ACCESS_RW,  Save_None,  0,                        0,          0,      "","",         0,            0 }, //总线控制权限
	entry2012[1] = { 0x1,	"FielsbusRemote",               "",     DT_Bool,    ACCESS_RW,  Save_None,  (uint8*)&pSysShareData->SCCW ,                    0,          1,      "","",         0,             1 }, //设备上电(上升沿触发)、下降沿触发下电
	entry2012[2] = { 0x2,	"Power",                        "",     DT_Bool,    ACCESS_RW,  Save_None,  (uint8*)&pSysShareData->SCCW ,                    0,          1,      "","",         1,             1 }, //工艺切换(上升沿触发)
	entry2012[3] = { 0x3,	"MPSiwch",                      "",     DT_Bool,    ACCESS_RW,  Save_None,  (uint8*)&pSysShareData->SCCW ,                    0,          1,      "","",         2,             1 },
	//0x04
	entry2012[4] = { 0x5,	"FaultReset",                    "",     DT_Bool,    ACCESS_RW,  Save_None,  (uint8*)&pSysShareData->SCCW ,                    0,          1,      "","",         4,             1 }, //错误清除(上升沿触发)
	//0x06-0x08     //
		//Byte1
	entry2012[5] = { 0x9,	"RunSequence",                  "",     DT_Bool,    ACCESS_RW,  Save_None,  (uint8*)&pSysShareData->SCCW + 1,                0,          1,      "","",        0,              1 },
	//{0xA,	               
	entry2012[6] = { 0xB,	"StopSequence",                 "",     DT_Bool,    ACCESS_RW,  Save_None,  (uint8*)&pSysShareData->SCCW + 1,                0,          1,      "","",        2,              1 },
	// {0xC,	"reserved_1_3",     
	entry2012[7] = { 0xD,	"IdNumber",                     "",     DT_Bool,    ACCESS_RW,  Save_None,  (uint8*)&pSysShareData->SCCW + 1,                0,          1,      "","",        4,              1 },
	entry2012[8] =//Byte2
	entry2012[9] = { 0x11,	"MoveToHomePos",                "",     DT_Bool,    ACCESS_RW,  Save_None,  (uint8*)&pSysShareData->SCCW + 2,                0,          1,      "","",        0,              1 },
	entry2012[10] = { 0x12,	"MoveToRefPos",                 "",     DT_Bool,    ACCESS_RW,  Save_None,  (uint8*)&pSysShareData->SCCW + 2,                0,          1,      "","",        1,              1 },
	entry2012[11] = { 0x13,	"JogForword",                   "",     DT_Bool,    ACCESS_RW,  Save_None,  (uint8*)&pSysShareData->SCCW + 2,                0,          1,      "","",        2,              1 },
	entry2012[12] = { 0x14,	"JogBackword",                  "",     DT_Bool,    ACCESS_RW,  Save_None,  (uint8*)&pSysShareData->SCCW + 2,                0,          1,      "","",        3,              1 },

	//Byte3  uint8 reserved_3_;   0x19-0x20
	entry2012[13] = { 0x21,	"TareYChannel",                 "",     DT_Bool,    ACCESS_RW,  Save_None,  (uint8*)&pSysShareData->SCCW + 4,                0,          1,      "","",        0,              1 },
	//Byte4 // reserved_4_1  \reserved_4_2 \reserved_4_3\ reserved_4_4\ reserved_4_5 \reserved_4_6 \reserved_4_7 : 1;  
	//Byte5 uint8 reserved_5_ : 1; 0x29-0x30
	//Byte6SCCW
	entry2012[14] = { 0x31,	"Continue1",                    "",     DT_Bool,    ACCESS_RW,  Save_None,  (uint8*)&pSysShareData->SCCW + 6,                0,          1,      "","",        0,              1 },
	entry2012[15] = { 0x32,	"Continue2",                    "",     DT_Bool,    ACCESS_RW,  Save_None,  (uint8*)&pSysShareData->SCCW + 6,                0,          1,      "","",        1,              1 },
	entry2012[16] = { 0x33,	"Continue3",                    "",     DT_Bool,    ACCESS_RW,  Save_None,  (uint8*)&pSysShareData->SCCW + 6,                0,          1,      "","",        2,              1 },
	entry2012[17] = { 0x34,	"Continue4",                    "",     DT_Bool,    ACCESS_RW,  Save_None,  (uint8*)&pSysShareData->SCCW + 6,                0,          1,      "","",        3,              1 },
	entry2012[18] = { 0x35,	"Continue5",                    "",     DT_Bool,    ACCESS_RW,  Save_None,  (uint8*)&pSysShareData->SCCW + 6,                0,          1,      "","",        4,              1 },
	entry2012[19] = { 0x36,	"Continue6",                    "",     DT_Bool,    ACCESS_RW,	Save_None,  (uint8*)&pSysShareData->SCCW + 6,                0,          1,      "","",        5,              1 },
	entry2012[20] = { 0x37,	"Continue7",                    "",     DT_Bool,    ACCESS_RW,	Save_None,  (uint8*)&pSysShareData->SCCW + 6,                0,          1,      "","",        6,              1 },
	entry2012[21] = { 0x38,	"Continue8",                    "",     DT_Bool,    ACCESS_RW,	Save_None,  (uint8*)&pSysShareData->SCCW + 6,                0,          1,      "","",        7,              1 },

	//Byte7     
	entry2012[22] = { 0x39,	"JumpToNextItem",               "",     DT_Bool,    ACCESS_RW,  Save_None,  (uint8*)&pSysShareData->SCCW + 7,                0,          1,      "","",        0,              1 },
	entry2012[23] =// 0x3A-0x40   reserved_07_01  -  reserved_07_07 
	//0x41-0x70     // reserve byte8-13 uint8 reserved18[4];
	 entry2012[24] = { 0x71,	"SelectedMP",                   "",       DT_U8,     ACCESS_RW,  Save_None,  &pSysShareData->SCCW.SelectedMP,   0,          255,      "","",      0,              0 },
	//0x79-0x80     //reservedByte15
	entry2012[25] = { 0x81,	"PagePlcIn",                    "",       DT_U8,     ACCESS_RW,  Save_None,  &pSysShareData->SCCW.PagePlcIn,    0,          255,      "","",      0,              0 },
	//0x89-0x90     //reservedByte15
	entry2012[26] = { 0x91,	"PagePressOut",                 "",       DT_U8,     ACCESS_RW,  Save_None,  &pSysShareData->SCCW.PagePressOut, 0,          255,      "","",      0,              0 },
};

//** Sdo 3012 Group Used to IO Control word  **/
SDOEntryDesc entry2013[11] =
{
	/**************  Key  **  Name  *********************, Alias* DataType * ObjAccess ************** pVar  ****************** Minimum *** Maximum ** ExtString ** BitOffset ** BitLength ***** */
	entry2013[0] = { 0x0,	"IO Control Word",              "",     DT_None,    ACCESS_RO,  Save_None,                                    0,           0,          0,      "","",         0,             0 },
	entry2013[1] = { 0x1,	"IoRemote",                     "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SICW.sSiCw ,           0,          1,      "","",         0,             1 },
	entry2013[2] = { 0x2,	"FaultReset",                   "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SICW.sSiCw ,           0,          1,      "","",         1,             1 },
	entry2013[3] = { 0x3,	"Power",                        "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SICW.sSiCw ,           0,          1,      "","",         2,             1 },
	entry2013[4] = { 0x4,	"StopSequence",                 "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SICW.sSiCw ,           0,          1,      "","",         3,             1 },
	entry2013[5] = { 0x5,	"RunSequence",                  "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SICW.sSiCw ,           0,          1,      "","",         4,             1 },
	entry2013[6] = { 0x6,	"WaitContinue",                 "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SICW.sSiCw ,           0,          1,      "","",         5,             1 },
	entry2013[7] = { 0x7,	"MoveToHomePos",                "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SICW.sSiCw ,           0,          1,      "","",         6,             1 },
	entry2013[8] = { 0x8,	"MoveToRefPos",                 "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SICW.sSiCw + 1,        0,          1,      "","",         1,             1 },
	entry2013[9] = { 0x9,	"JogForward",                   "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SICW.sSiCw ,           0,          1,      "","",         7,             1 },
	entry2013[10] = { 0xA,	"JogBackward",                  "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SICW.sSiCw + 1 ,       0,          1,      "","",         0,             1 },
};

SDOEntryDesc entry2014[25] =
{
	/*Key  **  Name  *********************, Alias* DataType * ObjAccess ************** pVar  ****************** Minimum *** Maximum ** ExtString ** BitOffset ** BitLength ***** */
	entry2014[0] = { 0x0,	"Fieldbus Common Control Word", "",     DT_None,    ACCESS_RO,  Save_None,  0,                        0,          0,      "","",         0,            0 }, //总线控制权限
	entry2014[1] = { 0x1,	"FielsbusRemote",               "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SFCW ,                    0,          1,      "","",         0,             1 }, //设备上电(上升沿触发)、下降沿触发下电
	entry2014[2] = { 0x2,	"Power",                        "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SFCW ,                    0,          1,      "","",         1,             1 }, //工艺切换(上升沿触发)
	entry2014[3] = { 0x3,	"MPSiwch",                      "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SFCW ,                    0,          1,      "","",         2,             1 },
	entry2014[4] = { 0x5,	"FaultReset",                   "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SFCW ,                    0,          1,      "","",         4,             1 }, //错误清除(上升沿触发)

	//Byte1
	entry2014[5] = { 0x9,	"RunSequence",                  "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SFCW + 1,                0,          1,      "","",        0,              1 },
	entry2014[6] = { 0xB,	"StopSequence",                 "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SFCW + 1,                0,          1,      "","",        2,              1 },
	entry2014[7] = { 0xD,	"IdNumber",                     "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SFCW + 1,                0,          1,      "","",        4,              1 },

	//Byte2
	entry2014[8] = { 0x11,	"MoveToHomePos",                "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SFCW + 2,                0,          1,      "","",        0,              1 },
	entry2014[9] = { 0x12,	"MoveToRefPos",                 "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SFCW + 2,                0,          1,      "","",        1,              1 },
	entry2014[10] = { 0x13,	"JogForword",                   "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SFCW + 2,                0,          1,      "","",        2,              1 },
	entry2014[11] = { 0x14,	"JogBackword",                  "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SFCW + 2,                0,          1,      "","",        3,              1 },
	//Byte3  uint8 reserved_3_;   0x19-0x20
	entry2014[12] = { 0x21,	"TareYChannel",                 "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SFCW + 4,                0,          1,      "","",        0,              1 },
	//Byte4 // reserved_4_1  \reserved_4_2 \reserved_4_3\ reserved_4_4\ reserved_4_5 \reserved_4_6 \reserved_4_7 : 1;  
	//Byte5 uint8 reserved_5_ : 1; 0x29-0x30
	//Byte6
	entry2014[13] = { 0x31,	"Continue1",                    "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SFCW + 6,                0,          1,      "","",        0,              1 },
	entry2014[14] = { 0x32,	"Continue2",                    "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SFCW + 6,                0,          1,      "","",        1,              1 },
	entry2014[15] = { 0x33,	"Continue3",                    "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SFCW + 6,                0,          1,      "","",        2,              1 },
	entry2014[16] = { 0x34,	"Continue4",                    "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SFCW + 6,                0,          1,      "","",        3,              1 },
	entry2014[17] = { 0x35,	"Continue5",                    "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SFCW + 6,                0,          1,      "","",        4,              1 },
	entry2014[18] = { 0x36,	"Continue6",                    "",     DT_Bool,    ACCESS_RO,	Save_None,  (uint8*)&pSysShareData->SFCW + 6,                0,          1,      "","",        5,              1 },
	entry2014[19] = { 0x37,	"Continue7",                    "",     DT_Bool,    ACCESS_RO,	Save_None,  (uint8*)&pSysShareData->SFCW + 6,                0,          1,      "","",        6,              1 },
	entry2014[20] = { 0x38,	"Continue8",                    "",     DT_Bool,    ACCESS_RO,	Save_None,  (uint8*)&pSysShareData->SFCW + 6,                0,          1,      "","",        7,              1 },

	//Byte7     
	entry2014[21] = { 0x39,	"JumpToNextItem",               "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SFCW + 7,                0,          1,      "","",        0,              1 },
	// 0x3A-0x40   reserved_07_01  -  reserved_07_07 
	//0x41-0x70     // reserve byte8-13 uint8 reserved18[4];
	entry2014[22] = { 0x71,	"SelectedMP",                   "",       DT_U8,     ACCESS_RO,  Save_None,  &pSysShareData->SFCW.SelectedMP,   0,          255,      "","",      0,              0 },
	//0x79-0x80     //reservedByte15
	entry2014[23] = { 0x81,	"PagePlcIn",                    "",       DT_U8,     ACCESS_RO,  Save_None,  &pSysShareData->SFCW.PagePlcIn,    0,          255,      "","",      0,              0 },
	//0x89-0x90     //reservedByte15
	entry2014[24] = { 0x91,	"PagePressOut",                 "",       DT_U8,     ACCESS_RO,  Save_None,  &pSysShareData->SFCW.PagePressOut, 0,          255,      "","",      0,              0 }
};

//** Sdo 3012 Group Used to Filedbus Status word  **/
SDOEntryDesc entry3011[12] =
{
	/*************** Key  **  Name  ****************************, **Alias***** DataType ***** ObjAccess ************** pVar  ******************************* Minimum *** Maximum ** ExtString ** BitOffset ** BitLength ***** */
	entry3011[0] = { 0x0,	"Explorer Status Word",                  "",     DT_None,    ACCESS_RO,  Save_None,   0,                                          0,            0,      "","",       0,             0 },
	entry3011[1] = { 0x1,	"System FSM",                            "",     DT_U8,      ACCESS_RO,  Save_None,   &pSysShareData->sExSW.eSysState,            0,            16,     "","EnumList#0:PreLoadPara;1:SelfCheck;2:Init;3:Ready;4:Switch_Profile;5:Manual_Op;6:Sequence;15:Fault",       0,             0 },
	entry3011[2] = { 0x2,	"System Control Mode",                   "",     DT_U8,      ACCESS_RO,  Save_None,   &pSysShareData->sExSW.eSysControlMode,      0,            10,     "","EnumList#0:Explorer;1:Fieldbus;2:Io;3:Fieldbus_IO",       0,             0 },
	entry3011[3] = { 0x3,	"Cycle Run Mode",                        "",     DT_Bool,    ACCESS_RW,  Save_None,   &pSysShareData->bCycleRun,                  0,            1,     "","",       0,             0 },
	entry3011[4] = { 0x4,	"Cycle Switch Profile Mode",             "",     DT_Bool,    ACCESS_RW,  Save_None,   &pSysShareData->bCycleSwitchProfile,        0,            1,     "","",       0,             0 },
	entry3011[5] = { 0x5,	"Cycle Count",                           "",     DT_U32,     ACCESS_RO,  Save_User,   &pSysShareData->ulCount,                    0,   0xffffffff,     "","",       0,             0 },
	entry3011[6] = { 0x6,	"Switch Profile Current Time(ms)",       "",     DT_F32,     ACCESS_RO,  Save_User,   &pSysShareData->sSwitchProfTime.CurTime,    0,            1,     "","",       0,             0},
	entry3011[7] = { 0x7,	"Switch Profile MaxTime Time(ms)",       "",     DT_F32,     ACCESS_RO,  Save_User,   &pSysShareData->sSwitchProfTime.MaxTime,    0,   0xffffffff,     "","",       0,             0},
	entry3011[8] = { 0x8,	"Switch Profile MinTime Time(ms)",       "",     DT_F32,     ACCESS_RO,  Save_User,   &pSysShareData->sSwitchProfTime.MinTime,    0,            1,     "","",       0,             0},
	entry3011[9] = { 0x9,	"Switch Profile AverageTime Time(ms)",   "",     DT_F32,     ACCESS_RO,  Save_User,   &pSysShareData->sSwitchProfTime.AverageTime,0,            1,     "","",       0 ,       0},

	entry3011[10] = { 0x60,	"Actived ProfId",                        "",     DT_U8,      ACCESS_RO,  Save_User,   &CurActiveProfId,                           0,          255,     "","",       0,             0 },
	entry3011[11] = { 0x61,	"Actived ProfId Name",                   "",     DT_Str,     ACCESS_RO,  Save_User,   &sActivePrfName,                            0,            0,     "","",       0,             0 }
};

//** Sdo 3012 Group Used to Filedbus Status word  **/
SDOEntryDesc entry3012[38] =
{
	/**************** Key  **  Name  *********************, Alias* DataType * ObjAccess ************** pVar  ****************** Minimum *** Maximum ** ExtString ** BitOffset ** BitLength ***** */
	entry3012[0] = { 0x0,	"Common Status Word",           "",     DT_None,    ACCESS_RO,  Save_None,  0,                                0,          0,      "","",       0,             0 },
	entry3012[1] = { 0x1,	"FieldBusRemoted",              "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SCSW,                    0,          1,      "","",       0,             1 },
	entry3012[2] = { 0x2,	"PowerState",                   "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SCSW,                    0,          1,      "","",       1,             1 },
	entry3012[3] = { 0x3,	"MPSiwched",                    "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SCSW,                    0,          1,      "","",       2,             1 },
	entry3012[4] = { 0x4,	"SystemReady",                  "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SCSW,                    0,          1,      "","",       3,             1 },
	entry3012[5] = { 0x5,	"SystemFault",                  "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SCSW,                    0,          1,      "","",       4,             1 },
	entry3012[6] = { 0x6,	"SystemWarm",                   "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SCSW,                    0,          1,      "","",       5,             1 },
	entry3012[7] = { 0x7,	"eStopTrig",                    "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SCSW,                    0,          1,      "","",       6,             1 },
	entry3012[8] = { 0x8,	"gStopTrig",                    "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SCSW,                    0,          1,      "","",       7,             1 },

	entry3012[9] = { 0x9,	"SequenceBusy",                 "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SCSW + 1 ,                  0,          1,      "","",       0,              1 },
	entry3012[10] = { 0xA,	"SequencePaused",               "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SCSW + 1,                  0,          1,      "","",        1,              1 },
	entry3012[11] = { 0xB,	"SequenceEnd",                  "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SCSW + 1,                  0,          1,      "","",        2,              1 },
	entry3012[12] = { 0xC,	"SequenceAbnormal",             "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SCSW + 1,                  0,          1,      "","",        3,              1 },
	entry3012[13] = { 0xD,	"PartIdUsed",                   "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SCSW + 1,                  0,          1,      "","",        4,              1 },
	//{},
	entry3012[14] = { 0xF,	"ResultOK",                     "",     DT_Bool,     ACCESS_RO,	Save_None,  (uint8*)&pSysShareData->SCSW + 1,                  0,          1,      "","",        6,              1 },
	entry3012[15] = { 0x10,	"ResultNOK",                    "",     DT_Bool,     ACCESS_RO,	Save_None,  (uint8*)&pSysShareData->SCSW + 1,                  0,          1,      "","",        7,              1 },

	entry3012[16] = { 0x11,	"HomePosReached",               "",     DT_Bool,    ACCESS_RO,  Save_None, (uint8*)&pSysShareData->SCSW + 2,                0,          1,      "","",        0,              1 },
	entry3012[17] = { 0x12,	"RefPosReached",                "",     DT_Bool,    ACCESS_RO,  Save_None, (uint8*)&pSysShareData->SCSW + 2,                0,          1,      "","",        1,              1 },
	entry3012[18] = { 0x13,	"reserved_02_2",                "",     DT_Bool,    ACCESS_RO,  Save_None, (uint8*)&pSysShareData->SCSW + 2,                0,          1,      "","",        2,              1 },
	entry3012[19] = { 0x14,	"reserved_02_3",                "",     DT_Bool,    ACCESS_RO,  Save_None, (uint8*)&pSysShareData->SCSW + 2,                0,          1,      "","",        3,              1 },
	entry3012[20] = { 0x15,	"reserved_02_4",                "",     DT_Bool,    ACCESS_RO,  Save_None, (uint8*)&pSysShareData->SCSW + 2,                0,          1,      "","",        4,              1 },
	entry3012[21] = { 0x16,	"MotorStandStill",              "",     DT_Bool,    ACCESS_RO,  Save_None, (uint8*)&pSysShareData->SCSW + 2,                0,          1,      "","",        5,              1 },
	entry3012[22] = { 0x17,	"MinPosReached",                "",     DT_Bool,     ACCESS_RO,	Save_None, (uint8*)&pSysShareData->SCSW + 2,                0,          1,      "","",        6,              1 },
	entry3012[23] = { 0x18,	"MaxPosReached",                "",     DT_Bool,     ACCESS_RO,	Save_None, (uint8*)&pSysShareData->SCSW + 2,                0,          1,      "","",        7,              1 },
	// {},                                //0x19-0x20  Byte 3
	entry3012[24] = { 0x21,	"TareYChanneled",               "",     DT_Bool,     ACCESS_RO,	Save_None,  (uint8*)&pSysShareData->SCSW + 4,                0,          1,      "","",        0,              1 },
	//   {},{},{},{},{},{},{},            //0x22-0x28     //4.1 4.2 4.3 4.4   4.5 4.6 4.7
	// {},                                //0x29-0x30  Byte 5
	entry3012[25] = { 0x31,	"Wait1",                        "",       DT_Bool,    ACCESS_RO,  Save_None,   (uint8*)&pSysShareData->SCSW + 6,              0,          1,      "","",        0,              1 },
	entry3012[26] = { 0x32,	"Wait2",                        "",       DT_Bool,    ACCESS_RO,  Save_None,   (uint8*)&pSysShareData->SCSW + 6,              0,          1,      "","",        1,              1 },
	entry3012[27] = { 0x33,	"Wait3",                        "",       DT_Bool,    ACCESS_RO,  Save_None,   (uint8*)&pSysShareData->SCSW + 6,              0,          1,      "","",        2,              1 },
	entry3012[28] = { 0x34,	"Wait4",                        "",       DT_Bool,    ACCESS_RO,  Save_None,   (uint8*)&pSysShareData->SCSW + 6,              0,          1,      "","",        3,              1 },
	entry3012[29] = { 0x35,	"Wait5",                        "",       DT_Bool,    ACCESS_RO,  Save_None,   (uint8*)&pSysShareData->SCSW + 6,              0,          1,      "","",        4,              1 },
	entry3012[30] = { 0x36,	"Wait6",                        "",       DT_Bool,    ACCESS_RO,  Save_None,   (uint8*)&pSysShareData->SCSW + 6,              0,          1,      "","",        5,              1 },
	entry3012[31] = { 0x37,	"Wait7",                        "",       DT_Bool,    ACCESS_RO,  Save_None,   (uint8*)&pSysShareData->SCSW + 6,              0,          1,      "","",        6,              1 },
	entry3012[32] = { 0x38,	"Wait8",                        "",       DT_Bool,    ACCESS_RO,  Save_None,   (uint8*)&pSysShareData->SCSW + 6,              0,          1,      "","",        7,              1 },
	//       
	entry3012[33] = { 0x39,	"HadJumpToNextItem",            "",       DT_Bool,    ACCESS_RO,  Save_None,   (uint8*)&pSysShareData->SCSW + 7,              0,          1,      "","",        0,              1 },
	//   {},                              //0x3A-0x40     //
	//   {},                              //0x41-0x60     // reserve byte8-11 uint8 reserved18[4];
	entry3012[34] = { 0x61,	"SysFaultCode",                 "",       DT_U16,    ACCESS_RO,  Save_None,   (uint8*)&pSysShareData->SCSW + 12,              0,          0xFFFF,      "","",     0,              16 },
	entry3012[35] = { 0x71,	"ActiveMPId",                   "",       DT_U8,     ACCESS_RO,  Save_User,   (uint8*)&pSysShareData->SCSW + 14,              0,          255,      "","",        0,              8 },
	//0x79-0x80     //reservedByte15
	entry3012[36] = { 0x81,	"Mirror_PagePlcIn",             "",       DT_U8,     ACCESS_RO,  Save_None,   (uint8*)&pSysShareData->SCSW + 16,              0,          255,      "","",        0,              8 },
	//0x89-0x90     //reservedByte15
	entry3012[37] = { 0x91,	"Mirror_PagePressOut",          "",       DT_U8,     ACCESS_RO,  Save_None,   (uint8*)&pSysShareData->SCSW + 18,              0,          255,      "","",        0,              8 }
};

//** Sdo 3012 Group Used to IO Status word  **/
SDOEntryDesc entry3013[12] =
{
	/*************** Key  **  Name  *********************, Alias* DataType * ObjAccess ************** pVar  ****************** Minimum *** Maximum ** ExtString ** BitOffset ** BitLength ***** */
	entry3013[0] = { 0x0,	"IO Status Word",               "",     DT_None,    ACCESS_RW,  Save_None,                               0,           0,          0,      "","",         0,             0 },
	entry3013[1] = { 0x1,	"IoRemoted",                     "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SISW ,           0,          1,      "","",         0,             1 },
	entry3013[2] = { 0x2,	"FaultState",                    "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SISW ,           0,          1,      "","",         1,             1 },
	entry3013[3] = { 0x3,	"PowerState",                    "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SISW ,           0,          1,      "","",         2,             1 },
	entry3013[4] = { 0x4,	"ResultOK",                      "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SISW ,           0,          1,      "","",         3,             1 },
	entry3013[5] = { 0x5,	"ResultNOK",                     "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SISW ,           0,          1,      "","",         4,             1 },
	entry3013[6] = { 0x6,	"SequenceEnd",                   "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SISW ,           0,          1,      "","",         5,             1 },
	entry3013[7] = { 0x7,	"SequenceRunning",               "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SISW ,           0,          1,      "","",         6,             1 },
	entry3013[8] = { 0x8,	"Wait",                          "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SISW ,           0,          1,      "","",         7,             1 },
	entry3013[9] = { 0x9,	"HomePosReached",                "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SISW + 1,        0,          1,      "","",         0,             1 },
	entry3013[10] = { 0xA,	"RefPosReached",                 "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SISW + 1 ,       0,          1,      "","",         2,             1 },
	entry3013[11] = { 0xB,	"IsStandStill",                  "",     DT_Bool,    ACCESS_RO,  Save_None,  (uint8*)&pSysShareData->SISW + 1 ,       0,          1,      "","",         1,             1 }
};

/** Sdo 4002 Group Curve Result  **/
SDOEntryDesc entry4002[15] =
{
	/**************** Key  **  Name  *********************, Alias* DataType * ObjAccess ************** pVar  ****************** Minimum *** Maximum ** ExtString ** BitOffset ** BitLength ***** */
	entry4002[0] = { 0x00,	"Curve Result",               "",     DT_None,   ACCESS_RO,  Save_None,  0,                           0,          0,      "","",          0,             0 },
	entry4002[1] = { 0x01,	"Curve Xmin_X",               "",     DT_F32,    ACCESS_RO,  Save_None,  &CurResult.Xmin_X ,           0,          1,      "","",         0,             0 },
	entry4002[2] = { 0x02,	"Curve Xmin_Y",               "",     DT_F32,    ACCESS_RO,  Save_None,  &CurResult.Xmin_Y ,           0,          1,      "","",         0,             0 },
	entry4002[3] = { 0x03,	"Curve Xmax_X",               "",     DT_F32,    ACCESS_RO,  Save_None,  &CurResult.Xmax_X ,           0,          1,      "","",         0,             0 },
	entry4002[4] = { 0x04,	"Curve Xmax_Y",               "",     DT_F32,    ACCESS_RO,  Save_None,  &CurResult.Xmax_Y ,           0,          1,      "","",         0,             0 },
	entry4002[5] = { 0x05,	"Curve Ymin_X",               "",     DT_F32,    ACCESS_RO,  Save_None,  &CurResult.Ymin_X ,           0,          1,      "","",         0,             0 },
	entry4002[6] = { 0x06,	"Curve Ymin_Y",               "",     DT_F32,    ACCESS_RO,  Save_None,  &CurResult.Ymin_Y ,           0,          1,      "","",         0,             0 },
	entry4002[7] = { 0x07,	"Curve Ymax_X",               "",     DT_F32,    ACCESS_RO,  Save_None,  &CurResult.Ymax_X ,           0,          1,      "","",         0,             0 },
	entry4002[8] = { 0x08,	"Curve Ymax_Y",               "",     DT_F32,    ACCESS_RO,  Save_None,  &CurResult.Ymax_Y ,           0,          1,      "","",         0,             0 },
	entry4002[9] = { 0x09,	"Curve PeakPeakX",            "",     DT_F32,    ACCESS_RO,  Save_None,  &CurResult.PeakPeakX,         0,          1,      "","",         0,             0 },
	entry4002[11] = { 0x0A,	"Curve PeakPeakY",            "",     DT_F32,    ACCESS_RO,  Save_None,  &CurResult.PeakPeakY ,        0,          1,      "","",         0,             0 },
	entry4002[12] = { 0x0B,	"Curve Tmax_T",               "",     DT_F32,    ACCESS_RO,  Save_None,  &CurResult.Tmax_T ,           0,          1,      "","",         0,             0 },
	entry4002[13] = { 0x0C,	"Curve Tmax_X",               "",     DT_F32,    ACCESS_RO,  Save_None,  &CurResult.Tmax_X ,           0,          1,      "","",         0,             0 },
	entry4002[14] = { 0x0D,	"Curve Tmax_Y",               "",     DT_F32,    ACCESS_RO,  Save_None,  &CurResult.Tmax_Y ,           0,          1,      "","",         0,             0 }
};

/** Sdo 4003 Return Result  **/
SDOEntryDesc entry4003[] =
{
	/**************** Key  **  Name  *********************, Alias* DataType * ObjAccess ************** pVar  ****************** Minimum *** Maximum ** ExtString ** BitOffset ** BitLength ***** */
	entry4003[0] = { 0x00,	"Return Result",                "",     DT_None,   ACCESS_RO,  Save_None,  0,									 0,     0,      "","",     0,             0 },
	entry4003[1] = { 0x01,	"Return ReturnState",           "",     DT_Bool,   ACCESS_RO,  Save_None,  &pReturnPoint.ReturnState ,			 0,     1,      "","",     0,             0 },
	entry4003[2] = { 0x02,	"Return ReturnType",            "",     DT_U8,     ACCESS_RO,  Save_None,  &pReturnPoint.ReturnType ,			 0,     1,      "","",     0,             0 },
	entry4003[3] = { 0x03,	"Return ReturnIndex",           "",     DT_U32,    ACCESS_RO,  Save_None,  &pReturnPoint.ReturnIndex ,			 0,     1,      "","",     0,             0 },
	entry4003[4] = { 0x04,	"Return ReturnStop",            "",     DT_Bool,   ACCESS_RO,  Save_None,  &pReturnPoint.ReturnStop ,			 0,     1,      "","",     0,             0 },
	entry4003[5] = { 0x05,	"Return fXmax",                 "",     DT_F32,    ACCESS_RO,  Save_None,  &pSMeasure_Returntext.fXmax ,		 0,     1,      "","",     0,             0 },
	entry4003[6] = { 0x06,	"Return fXmin",                 "",     DT_F32,    ACCESS_RO,  Save_None,  &pSMeasure_Returntext.fXmin ,		 0,     1,      "","",     0,             0 },
	entry4003[7] = { 0x07,	"Return fYmax",                 "",     DT_F32,    ACCESS_RO,  Save_None,  &pSMeasure_Returntext.fYmax ,		 0,     1,      "","",     0,             0 },
	entry4003[8] = { 0x08,	"Return fYmin",                 "",     DT_F32,    ACCESS_RO,  Save_None,  &pSMeasure_Returntext.fYmin ,		 0,     1,      "","",     0,             0 },
};

#if (PRODUCT_LINE != 2)
#define     AVALIABLE_ENTRY_COUNT    44
#define     FACTORY_ENTRY_COUNT      30
#define     SET_ENTRY_COUNT          30
#else
#define     AVALIABLE_ENTRY_COUNT    38
#define     FACTORY_ENTRY_COUNT      26
#define     SET_ENTRY_COUNT          26
#endif
//SDOEntryDesc entry5000[AVALIABLE_ENTRY_COUNT] =
//{
//    /**************** Key  **  Name  *********************, Alias* DataType * ObjAccess ************************************************************* pVar  ****************** Minimum *** Maximum ** ExtString ** BitOffset ** BitLength ***** */
//    entry5000[0] =  { 0x00,	"Sensors Avaliable Para",     "",     DT_None,   ACCESS_RO,  Save_None,                                                         0,                  0,          0,      "","",         0,             0 },
//    entry5000[1] =  { 0x01,	"Sensors Actived Count",      "",     DT_U8,     ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.SenActivedCount ,                        0,          1,      "","",         0,             0 },
//    entry5000[2] =  { 0x02,	"Name",                       "",     DT_Str,    ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[0].spName ,                  0,          1,      "","",         0,             0},
//    entry5000[3] =  { 0x03,	"Type",                       "",     DT_I16,    ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[0].eSensorType ,             0,          1,      "","EnumList#1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",         0,             0},
//    entry5000[4] =  { 0x04,	"Actived",                    "",     DT_Bool,   ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[0].bSensorActive ,           0,          1,      "","",         0,             0},
//    entry5000[5] =  { 0x05,	"Position",                   "",     DT_F32,    ACCESS_RO,  Save_None,   pSysShareData->sSysAvaliablePara.sSensorUnit[0].pfSensorVar ,             0,          1,      "","",         0,             0},
//    entry5000[6] =  { 0x06,	"Maxinum",                    "",     DT_F32,    ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[0].sLimRange.Maxinum ,       0,          1,      "","",         0,             0},
//    entry5000[7] =  { 0x07,	"Mininum",                    "",     DT_F32,    ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[0].sLimRange.Mininum ,       0,          1,      "","",         0,             0},
//    entry5000[8] =  { 0x08,	"Name",                       "",     DT_Str,    ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[1].spName ,                  0,          1,      "","",         0,             0},
//    entry5000[9] =  { 0x09,	"Type",                       "",     DT_I16,    ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[1].eSensorType ,             0,          1,      "","EnumList#1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",         0,             0},
//    entry5000[10] = { 0x0A,	"Actived",                    "",     DT_Bool,   ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[1].bSensorActive ,           0,          1,      "","",         0,             0},
//    entry5000[11] = { 0x0B,	"Velocity",                   "",     DT_F32,    ACCESS_RO,  Save_None,  pSysShareData->sSysAvaliablePara.sSensorUnit[1].pfSensorVar ,             0,          1,      "","",         0,             0},
//    entry5000[12] = { 0x0C,	"Maxinum",                    "",     DT_F32,    ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[1].sLimRange.Maxinum ,       0,          1,      "","",         0,             0},
//    entry5000[13] = { 0x0D,	"Mininum",                    "",     DT_F32,    ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[1].sLimRange.Mininum ,       0,          1,      "","",         0,             0},
//    entry5000[14] = { 0x0E,	"Name",                       "",     DT_Str,    ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[2].spName ,                  0,          1,      "","",         0,             0},
//    entry5000[15] = { 0x0F,	"Type",                       "",     DT_I16,    ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[2].eSensorType ,             0,          1,      "","EnumList#1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",         0,             0},
//    entry5000[16] = { 0x10,	"Actived",                    "",     DT_Bool,   ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[2].bSensorActive ,           0,          1,      "","",         0,             0},
//    entry5000[17] = { 0x11,	"Acc",                        "",     DT_F32,    ACCESS_RO,  Save_None,  pSysShareData->sSysAvaliablePara.sSensorUnit[2].pfSensorVar ,             0,          1,      "","",         0,             0},
//    entry5000[18] = { 0x12,	"Maxinum",                    "",     DT_F32,    ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[2].sLimRange.Maxinum ,       0,          1,      "","",         0,             0},
//    entry5000[19] = { 0x13,	"Mininum",                    "",     DT_F32,    ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[2].sLimRange.Mininum ,       0,          1,      "","",         0,             0},
//    entry5000[20] = { 0x14,	"Name",                       "",     DT_Str,    ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[3].spName ,                  0,          1,      "","",         0,             0},
//    entry5000[21] = { 0x15,	"Type",                       "",     DT_I16,    ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[3].eSensorType ,             0,          1,      "","EnumList#1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",         0,             0},
//    entry5000[22] = { 0x16,	"Actived",                    "",     DT_Bool,   ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[3].bSensorActive ,           0,          1,      "","",         0,             0},
//    entry5000[23] = { 0x17,	"Current",                    "",     DT_F32,    ACCESS_RO,  Save_None,  pSysShareData->sSysAvaliablePara.sSensorUnit[3].pfSensorVar ,             0,          1,      "","",         0,             0},
//    entry5000[24] = { 0x18,	"Maxinum",                    "",     DT_F32,    ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[3].sLimRange.Maxinum ,       0,          1,      "","",         0,             0},
//    entry5000[25] = { 0x19,	"Mininum",                    "",     DT_F32,    ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[3].sLimRange.Mininum ,       0,          1,      "","",         0,             0},
//    entry5000[26] = { 0x1A,	"Name",                       "",     DT_Str,    ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[4].spName ,                  0,          1,      "","",         0,             0},
//    entry5000[27] = { 0x1B,	"Type",                       "",     DT_I16,    ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[4].eSensorType ,             0,          1,      "","EnumList#1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",         0,             0},
//    entry5000[28] = { 0x1C,	"Actived",                    "",     DT_Bool,   ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[4].bSensorActive ,           0,          1,      "","",         0,             0},
//    entry5000[29] = { 0x1D,	"Torque",                     "",     DT_F32,    ACCESS_RO,  Save_None,  pSysShareData->sSysAvaliablePara.sSensorUnit[4].pfSensorVar ,             0,          1,      "","",         0,             0},
//    entry5000[30] = { 0x1E,	"Maxinum",                    "",     DT_F32,    ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[4].sLimRange.Maxinum ,       0,          1,      "","",         0,             0},
//    entry5000[31] = { 0x1F,	"Mininum",                    "",     DT_F32,    ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[4].sLimRange.Mininum ,       0,          1,      "","",         0,             0},
//    entry5000[32] = { 0x20,	"Name",                       "",     DT_Str,    ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[5].spName ,                  0,          1,      "","",         0,             0},
//    entry5000[33] = { 0x21,	"Type",                       "",     DT_I16,    ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[5].eSensorType ,             0,          1,      "","EnumList#1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",         0,             0},
//    entry5000[34] = { 0x22,	"Actived",                    "",     DT_Bool,   ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[5].bSensorActive ,           0,          1,      "","",         0,             0},
//    entry5000[35] = { 0x23,	"Forece",                     "",     DT_F32,    ACCESS_RO,  Save_None,  pSysShareData->sSysAvaliablePara.sSensorUnit[5].pfSensorVar ,             0,          1,      "","",         0,             0},
//    entry5000[36] = { 0x24,	"Maxinum",                    "",     DT_F32,    ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[5].sLimRange.Maxinum ,       0,          1,      "","",         0,             0},
//    entry5000[37] = { 0x25,	"Mininum",                    "",     DT_F32,    ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[5].sLimRange.Mininum ,       0,          1,      "","",         0,             0},
//#if (PRODUCT_LINE != 2)
//    entry5000[38] = { 0x26,	"Name",                       "",     DT_Str,    ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[6].spName ,                  0,          1,      "","",         0,             0},
//    entry5000[39] = { 0x27,	"Type",                       "",     DT_I16,    ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[6].eSensorType ,             0,          1,      "","EnumList#1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",         0,             0},
//    entry5000[40] = { 0x28,	"Actived",                    "",     DT_Bool,   ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[6].bSensorActive ,           0,          1,      "","",         0,             0},
//    entry5000[41] = { 0x29,	"ExtShift",                   "",     DT_F32,    ACCESS_RO,  Save_None,  pSysShareData->sSysAvaliablePara.sSensorUnit[6].pfSensorVar ,             0,          1,      "","",         0,             0},
//    entry5000[42] = { 0x2A,	"Maxinum",                    "",     DT_F32,    ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[6].sLimRange.Maxinum ,       0,          1,      "","",         0,             0},
//    entry5000[43] = { 0x2B,	"Mininum",                    "",     DT_F32,    ACCESS_RO,  Save_None,  &pSysShareData->sSysAvaliablePara.sSensorUnit[6].sLimRange.Mininum ,       0,          1,      "","",         0,             0},
//#endif // DEBUG
//};

//#if (SYSTEM_MOTION_MODE == 1)
//SDOEntryDesc entry5001[AVALIABLE_ENTRY_COUNT] =
//{
//    /**************** Key  **  Name  *********************, Alias* DataType * ObjAccess ************************************************************* pVar  ****************** Minimum *** Maximum ** ExtString ** BitOffset ** BitLength ***** */
//    entry5001[0] =  { 0x00,	"Sensors Factory Para",     "",     DT_None,   ACCESS_RO,  Save_None,                                                                 0,                  0,          0,      "","",         0,             0 },
//    entry5001[1] =  { 0x01,	"Sensors Factory Count",      "",     DT_U8,     ACCESS_RO,  Save_None,  &sSysFactoryLimitPara.SenActivedCount ,                        0,          1,      "","",         0,             0 },
//    entry5001[2] =  { 0x02,	"Name",                       "",     DT_Str,    ACCESS_RO,  Save_None,  &sSysFactoryLimitPara.sSensorUnit[0].spName ,                  0,          0,      "","",         0,             0},
//    entry5001[3] =  { 0x03,	"Type",                       "",     DT_I16,    ACCESS_RO,  Save_None,  &sSysFactoryLimitPara.sSensorUnit[0].eSensorType ,             1,          7,      "","EnumList#1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",         0,             0},
//    entry5001[4] =  { 0x04,	"Maxinum",                    "",     DT_F32,    ACCESS_RW,  Save_None,  &sSysFactoryLimitPara.sSensorUnit[0].sLimRange.Maxinum ,       0,      10000,      "","",         0,             0},
//    entry5001[5] =  { 0x05,	"Mininum",                    "",     DT_F32,    ACCESS_RW,  Save_None,  &sSysFactoryLimitPara.sSensorUnit[0].sLimRange.Mininum ,  -10000,          0,      "","",         0,             0},
//    entry5001[6] =  { 0x06,	"Name",                       "",     DT_Str,    ACCESS_RO,  Save_None,  &sSysFactoryLimitPara.sSensorUnit[1].spName ,                  0,          0,      "","",         0,             0},
//    entry5001[7] =  { 0x07,	"Type",                       "",     DT_I16,    ACCESS_RO,  Save_None,  &sSysFactoryLimitPara.sSensorUnit[1].eSensorType ,             1,          7,      "","EnumList#1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",         0,             0},
//    entry5001[8] =  { 0x08,	"Maxinum",                    "",     DT_F32,    ACCESS_RW,  Save_None,  &sSysFactoryLimitPara.sSensorUnit[1].sLimRange.Maxinum ,       0,      10000,      "","",         0,             0},
//    entry5001[9] =  { 0x09,	"Mininum",                    "",     DT_F32,    ACCESS_RW,  Save_None,  &sSysFactoryLimitPara.sSensorUnit[1].sLimRange.Mininum ,  -10000,          0,      "","",         0,             0},
//    entry5001[10] = { 0x0A,	"Name",                       "",     DT_Str,    ACCESS_RO,  Save_None,  &sSysFactoryLimitPara.sSensorUnit[2].spName ,                  0,          0,      "","",         0,             0},
//    entry5001[11] = { 0x0B,	"Type",                       "",     DT_I16,    ACCESS_RO,  Save_None,  &sSysFactoryLimitPara.sSensorUnit[2].eSensorType ,             1,          7,      "","EnumList#1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",         0,             0},
//    entry5001[12] = { 0x0C,	"Maxinum",                    "",     DT_F32,    ACCESS_RW,  Save_None,  &sSysFactoryLimitPara.sSensorUnit[2].sLimRange.Maxinum ,       0,      10000,      "","",         0,             0},
//    entry5001[13] = { 0x0D,	"Mininum",                    "",     DT_F32,    ACCESS_RW,  Save_None,  &sSysFactoryLimitPara.sSensorUnit[2].sLimRange.Mininum ,  -10000,          1,      "","",         0,             0},
//    entry5001[14] = { 0x0E,	"Name",                       "",     DT_Str,    ACCESS_RO,  Save_None,  &sSysFactoryLimitPara.sSensorUnit[3].spName ,                  0,          0,      "","",         0,             0},
//    entry5001[15] = { 0x0F,	"Type",                       "",     DT_I16,    ACCESS_RO,  Save_None,  &sSysFactoryLimitPara.sSensorUnit[3].eSensorType ,             1,          7,      "","EnumList#1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",         0,             0},
//    entry5001[16] = { 0x10,	"Maxinum",                    "",     DT_F32,    ACCESS_RW,  Save_None,  &sSysFactoryLimitPara.sSensorUnit[3].sLimRange.Maxinum ,       0,      10000,      "","",         0,             0},
//    entry5001[17] = { 0x11,	"Mininum",                    "",     DT_F32,    ACCESS_RW,  Save_None,  &sSysFactoryLimitPara.sSensorUnit[3].sLimRange.Mininum ,  -10000,          0,      "","",         0,             0},
//    entry5001[18] = { 0x12,	"Name",                       "",     DT_Str,    ACCESS_RO,  Save_None,  &sSysFactoryLimitPara.sSensorUnit[4].spName ,                  0,          0,      "","",         0,             0},
//    entry5001[19] = { 0x13,	"Type",                       "",     DT_I16,    ACCESS_RO,  Save_None,  &sSysFactoryLimitPara.sSensorUnit[4].eSensorType ,             1,          7,      "","EnumList#1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",         0,             0},
//    entry5001[20] = { 0x14,	"Maxinum",                    "",     DT_F32,    ACCESS_RW,  Save_None,  &sSysFactoryLimitPara.sSensorUnit[4].sLimRange.Maxinum ,       0,      10000,      "","",         0,             0},
//    entry5001[21] = { 0x15,	"Mininum",                    "",     DT_F32,    ACCESS_RW,  Save_None,  &sSysFactoryLimitPara.sSensorUnit[4].sLimRange.Mininum ,  -10000,          0,      "","",         0,             0},
//    entry5001[22] = { 0x16,	"Name",                       "",     DT_Str,    ACCESS_RO,  Save_None,  &sSysFactoryLimitPara.sSensorUnit[5].spName ,                  0,          0,      "","",         0,             0},
//    entry5001[23] = { 0x17,	"Type",                       "",     DT_I16,    ACCESS_RO,  Save_None,  &sSysFactoryLimitPara.sSensorUnit[5].eSensorType ,             1,          7,      "","EnumList#1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",         0,             0},
//    entry5001[24] = { 0x18,	"Maxinum",                    "",     DT_F32,    ACCESS_RW,  Save_None,  &sSysFactoryLimitPara.sSensorUnit[5].sLimRange.Maxinum ,       0,      10000,      "","",         0,             0},
//    entry5001[25] = { 0x19,	"Mininum",                    "",     DT_F32,    ACCESS_RW,  Save_None,  &sSysFactoryLimitPara.sSensorUnit[5].sLimRange.Mininum ,  -10000,          0,      "","",         0,             0},
//#if (PRODUCT_LINE != 2)
//    entry5001[26] = { 0x1A,	"Name",                       "",     DT_Str,    ACCESS_RO,  Save_None,  &sSysFactoryLimitPara.sSensorUnit[6].spName ,                  0,          0,      "","",         0,             0},
//    entry5001[27] = { 0x1B,	"Type",                       "",     DT_I16,    ACCESS_RO,  Save_None,  &sSysFactoryLimitPara.sSensorUnit[6].eSensorType ,             1,          7,      "","EnumList#1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",         0,             0},
//    entry5001[28] = { 0x1C,	"Maxinum",                    "",     DT_F32,    ACCESS_RW,  Save_None,  &sSysFactoryLimitPara.sSensorUnit[6].sLimRange.Maxinum ,       0,      10000,      "","",         0,             0},
//    entry5001[29] = { 0x1D,	"Mininum",                    "",     DT_F32,    ACCESS_RW,  Save_None,  &sSysFactoryLimitPara.sSensorUnit[6].sLimRange.Mininum ,  -10000,          0,      "","",         0,             0},
//#endif // DEBUG
//};
//#endif // 0

//SDOEntryDesc entry5002[SET_ENTRY_COUNT] =
//{
//    /**************** Key  **  Name  *********************, Alias* DataType * ObjAccess ************************************************************* pVar  ****************** Minimum *** Maximum ** ExtString ** BitOffset ** BitLength ***** */
//    entry5002[0] =  { 0x00,	"Sensors Set Para",           "",     DT_None,   ACCESS_RO,  Save_None,                                                             0,                  0,          0,      "","",         0,             0 },
//    entry5002[1] =  { 0x01,	"Sensors Set Count",          "",     DT_U8,     ACCESS_RO,  Save_None,  &sSysSetLimitPara.SenActivedCount ,                        0,          1,      "","",         0,             0 },
//    entry5002[2] =  { 0x02,	"Name",                       "",     DT_Str,    ACCESS_RO,  Save_None,  &sSysSetLimitPara.sSensorUnit[0].spName ,                  0,          0,      "","",         0,             0},
//    entry5002[3] =  { 0x03,	"Type",                       "",     DT_I16,    ACCESS_RO,  Save_None,  &sSysSetLimitPara.sSensorUnit[0].eSensorType ,             1,          7,      "","EnumList#1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",         0,             0},
//    entry5002[4] =  { 0x04,	"Maxinum",                    "",     DT_F32,    ACCESS_RO,  Save_None,  &sSysSetLimitPara.sSensorUnit[0].sLimRange.Maxinum ,       0,      10000,      "","",         0,             0},
//    entry5002[5] =  { 0x05,	"Mininum",                    "",     DT_F32,    ACCESS_RO,  Save_None,  &sSysSetLimitPara.sSensorUnit[0].sLimRange.Mininum ,  -10000,          0,      "","",         0,             0},
//    entry5002[6] =  { 0x06,	"Name",                       "",     DT_Str,    ACCESS_RO,  Save_None,  &sSysSetLimitPara.sSensorUnit[1].spName ,                  0,          0,      "","",         0,             0},
//    entry5002[7] =  { 0x07,	"Type",                       "",     DT_I16,    ACCESS_RO,  Save_None,  &sSysSetLimitPara.sSensorUnit[1].eSensorType ,             1,          7,      "","EnumList#1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",         0,             0},
//    entry5002[8] =  { 0x08,	"Maxinum",                    "",     DT_F32,    ACCESS_RO,  Save_None,  &sSysSetLimitPara.sSensorUnit[1].sLimRange.Maxinum ,       0,      10000,      "","",         0,             0},
//    entry5002[9] =  { 0x09,	"Mininum",                    "",     DT_F32,    ACCESS_RO,  Save_None,  &sSysSetLimitPara.sSensorUnit[1].sLimRange.Mininum ,  -10000,          0,      "","",         0,             0},
//    entry5002[10] = { 0x0A,	"Name",                       "",     DT_Str,    ACCESS_RO,  Save_None,  &sSysSetLimitPara.sSensorUnit[2].spName ,                  0,          0,      "","",         0,             0},
//    entry5002[11] = { 0x0B,	"Type",                       "",     DT_I16,    ACCESS_RO,  Save_None,  &sSysSetLimitPara.sSensorUnit[2].eSensorType ,             1,          7,      "","EnumList#1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",         0,             0},
//    entry5002[12] = { 0x0C,	"Maxinum",                    "",     DT_F32,    ACCESS_RO,  Save_None,  &sSysSetLimitPara.sSensorUnit[2].sLimRange.Maxinum ,       0,      10000,      "","",         0,             0},
//    entry5002[13] = { 0x0D,	"Mininum",                    "",     DT_F32,    ACCESS_RO,  Save_None,  &sSysSetLimitPara.sSensorUnit[2].sLimRange.Mininum ,  -10000,          1,      "","",         0,             0},
//    entry5002[14] = { 0x0E,	"Name",                       "",     DT_Str,    ACCESS_RO,  Save_None,  &sSysSetLimitPara.sSensorUnit[3].spName ,                  0,          0,      "","",         0,             0},
//    entry5002[15] = { 0x0F,	"Type",                       "",     DT_I16,    ACCESS_RO,  Save_None,  &sSysSetLimitPara.sSensorUnit[3].eSensorType ,             1,          7,      "","EnumList#1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",         0,             0},
//    entry5002[16] = { 0x10,	"Maxinum",                    "",     DT_F32,    ACCESS_RO,  Save_None,  &sSysSetLimitPara.sSensorUnit[3].sLimRange.Maxinum ,       0,      10000,      "","",         0,             0},
//    entry5002[17] = { 0x11,	"Mininum",                    "",     DT_F32,    ACCESS_RO,  Save_None,  &sSysSetLimitPara.sSensorUnit[3].sLimRange.Mininum ,  -10000,          0,      "","",         0,             0},
//    entry5002[18] = { 0x12,	"Name",                       "",     DT_Str,    ACCESS_RO,  Save_None,  &sSysSetLimitPara.sSensorUnit[4].spName ,                  0,          0,      "","",         0,             0},
//    entry5002[19] = { 0x13,	"Type",                       "",     DT_I16,    ACCESS_RO,  Save_None,  &sSysSetLimitPara.sSensorUnit[4].eSensorType ,             1,          7,      "","EnumList#1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",         0,             0},
//    entry5002[20] = { 0x14,	"Maxinum",                    "",     DT_F32,    ACCESS_RO,  Save_None,  &sSysSetLimitPara.sSensorUnit[4].sLimRange.Maxinum ,       0,      10000,      "","",         0,             0},
//    entry5002[21] = { 0x15,	"Mininum",                    "",     DT_F32,    ACCESS_RO,  Save_None,  &sSysSetLimitPara.sSensorUnit[4].sLimRange.Mininum ,  -10000,          0,      "","",         0,             0},
//    entry5002[22] = { 0x16,	"Name",                       "",     DT_Str,    ACCESS_RO,  Save_None,  &sSysSetLimitPara.sSensorUnit[5].spName ,                  0,          0,      "","",         0,             0},
//    entry5002[23] = { 0x17,	"Type",                       "",     DT_I16,    ACCESS_RO,  Save_None,  &sSysSetLimitPara.sSensorUnit[5].eSensorType ,             1,          7,      "","EnumList#1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",         0,             0},
//    entry5002[24] = { 0x18,	"Maxinum",                    "",     DT_F32,    ACCESS_RO,  Save_None,  &sSysSetLimitPara.sSensorUnit[5].sLimRange.Maxinum ,       0,      10000,      "","",         0,             0},
//    entry5002[25] = { 0x19,	"Mininum",                    "",     DT_F32,    ACCESS_RO,  Save_None,  &sSysSetLimitPara.sSensorUnit[5].sLimRange.Mininum ,  -10000,          0,      "","",         0,             0},
//#if (PRODUCT_LINE != 2)
//    entry5002[26] = { 0x1A,	"Name",                       "",     DT_Str,    ACCESS_RO,  Save_None,  &sSysSetLimitPara.sSensorUnit[6].spName ,                  0,          0,      "","",         0,             0},
//    entry5002[27] = { 0x1B,	"Type",                       "",     DT_I16,    ACCESS_RO,  Save_None,  &sSysSetLimitPara.sSensorUnit[6].eSensorType ,             1,          7,      "","EnumList#1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",         0,             0},
//    entry5002[28] = { 0x1C,	"Maxinum",                    "",     DT_F32,    ACCESS_RO,  Save_None,  &sSysSetLimitPara.sSensorUnit[6].sLimRange.Maxinum ,       0,      10000,      "","",         0,             0},
//    entry5002[29] = { 0x1D,	"Mininum",                    "",     DT_F32,    ACCESS_RO,  Save_None,  &sSysSetLimitPara.sSensorUnit[6].sLimRange.Mininum ,  -10000,          0,      "","",         0,             0},
//#endif // DEBUG
//};

//驱动器总览信息
SDOEntryDesc entry5010[29] =
{
	/*Key  **  Name  **********************, Alias* DataType ***** ObjAccess ******************************************  pVar  ************************************************  Minimum  ******  Maximum ***********/
	entry5010[0] = { 0x0,   "System Drive Info",            "",    DT_None,    ACCESS_RO,  Save_None,                                               0, 0, 0, "", "", 0, 0 },
	entry5010[1] = { 0x1,	"Drive 1 Actived",              "",    DT_Bool,    ACCESS_RO,  Save_None,  &pSysShareData->AxisMc[0].sMcSetPara.bActived,                              0,              1,      "","",0,0 },
	entry5010[2] = { 0x2,	"Drive 1 Name",                 "",    DT_Str,     ACCESS_RO,  Save_User,  &pSysShareData->AxisMc[0].sMcSetPara.SensorName,                            0,              10000,      "","",0,0 },
	entry5010[3] = { 0x3,	"Drive 1 Type",                 "",    DT_U8,      ACCESS_RO,  Save_User,  &pSysShareData->AxisMc[0].sMcSetPara.SensorType,                            0,              10000,      "","EnumList#0:UnSet;1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",0,0 },
	entry5010[4] = { 0x4,	"Drive 1 Pos",                  "",    DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->AxisMc[0].Axis.LogicFbk.fPos,                               0,              10000,      "","",0,0 },
	entry5010[5] = { 0x5,	"Drive 1 Pos Out Range Watch",  "",    DT_Bool,    ACCESS_RO,  Save_User,  &pSysShareData->AxisMc[0].sMcSetPara.sPosLimRange.bWatchOutRange,            0,              1,      "","",0,0 },
	entry5010[6] = { 0x6,	"Drive 1 Pos Out Range Factor", "",    DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->AxisMc[0].sMcSetPara.sPosLimRange.fOutRangeFactor,         0.1,            100,      "","",0,0 },
	entry5010[7] = { 0x7,	"Drive 1 Pos LimRange Max",     "",    DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->AxisMc[0].sMcSetPara.sPosLimRange.Maxinum,               -1000,          10000,      "","",0,0 },
	entry5010[8] = { 0x8,	"Drive 1 Pos LimRange Min",     "",    DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->AxisMc[0].sMcSetPara.sPosLimRange.Mininum,               -1000,          10000,      "","",0,0 },

	entry5010[9] = { 0x9,	"Drive 1 Vel",                  "",    DT_F32,     ACCESS_RO,  Save_None,  &pSysShareData->AxisMc[0].Axis.LogicFbk.fVel,                                0,              10000,      "","",0,0 },
	entry5010[10] = { 0xA,	"Drive 1 Vel Out Range Watch",  "",    DT_Bool,    ACCESS_RO,  Save_User,  &pSysShareData->AxisMc[0].sMcSetPara.sVelLimRange.bWatchOutRange,           0,              1,      "","",0,0 },
	entry5010[11] = { 0xB,	"Drive 1 Vel Out Range Factor",  "",   DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->AxisMc[0].sMcSetPara.sVelLimRange.fOutRangeFactor,        0.1,            100,      "","",0,0 },
	entry5010[12] = { 0xC,	"Drive 1 Vel LimRange Max",     "",    DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->AxisMc[0].sMcSetPara.sVelLimRange.Maxinum,               -1000,          10000,      "","",0,0 },
	entry5010[13] = { 0xD,	"Drive 1 Vel LimRange Min",     "",    DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->AxisMc[0].sMcSetPara.sVelLimRange.Mininum,               -1000,          10000,      "","",0,0 },

	entry5010[14] = { 0xE,	"Drive 1 Acc",                  "",    DT_F32,     ACCESS_RO,  Save_None,  &pSysShareData->AxisMc[0].Axis.LogicFbk.fAcc,                               0,          10000,      "","",0,0 },
	entry5010[15] = { 0xF,	"Drive 1 Acc Out Range Watch", "",    DT_Bool,     ACCESS_RO,  Save_User,  &pSysShareData->AxisMc[0].sMcSetPara.sAccDecLimRange.bWatchOutRange,        0,              1,      "","",0,0 },
	entry5010[16] = { 0x10,	"Drive 1 Acc Out Range Factor",  "",   DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->AxisMc[0].sMcSetPara.sAccDecLimRange.fOutRangeFactor,     0.1,            100,      "","",0,0 },
	entry5010[17] = { 0x11,	"Drive 1 Acc LimRange Max",     "",    DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->AxisMc[0].sMcSetPara.sAccDecLimRange.Maxinum,            -1000,          10000,      "","",0,0 },
	entry5010[18] = { 0x12,	"Drive 1 Acc LimRange Min",     "",    DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->AxisMc[0].sMcSetPara.sAccDecLimRange.Mininum,            -1000,          10000,      "","",0,0 },

	entry5010[19] = { 0x13,	"Drive 1 RtTorque",             "",    DT_F32,     ACCESS_RO,  Save_None,  &pSysShareData->AxisMc[0].Axis.LogicFbk.fTorque,                             0,         10000,      "","",0,0 },
	entry5010[20] = { 0x14,	"Torque Out Range Watch",       "",    DT_Bool,    ACCESS_RO,  Save_User,  &pSysShareData->AxisMc[0].sMcSetPara.sTorqueLimRange.bWatchOutRange,         0,              1,      "","",0,0 },
	entry5010[21] = { 0x15,	"Torque Out Range Factor",      "",    DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->AxisMc[0].sMcSetPara.sTorqueLimRange.fOutRangeFactor,     0.1,            100,      "","",0,0 },
	entry5010[22] = { 0x16,	"Drive 1 Torque LimRange Max",  "",    DT_F32,     ACCESS_RO,  Save_None,  &pSysShareData->AxisMc[0].sMcSetPara.sTorqueLimRange.Maxinum,            -1000,          10000,      "","",0,0 },
	entry5010[23] = { 0x17,	"Drive 1 Torque LimRange Min",  "",    DT_F32,     ACCESS_RO,  Save_None,  &pSysShareData->AxisMc[0].sMcSetPara.sTorqueLimRange.Mininum,            -1000,          10000,      "","",0,0 },

	entry5010[24] = { 0x18,	"Drive 1 RtCurrent",            "",    DT_F32,     ACCESS_RO,  Save_None,  &pSysShareData->AxisMc[0].Axis.LogicFbk.fCurrent,                            0,          10000,      "","",0,0 },
	entry5010[25] = { 0x19,	"Drive 1 Current Out Range Watch","",  DT_Bool,    ACCESS_RO,  Save_User,  &pSysShareData->AxisMc[0].sMcSetPara.sCurrentLimRange.bWatchOutRange,        0,              1,      "","",0,0 },
	entry5010[26] = { 0x1A,	"Drive 1 Current Out Range Factor","", DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->AxisMc[0].sMcSetPara.sCurrentLimRange.fOutRangeFactor,     0.1,            100,      "","",0,0 },
	entry5010[27] = { 0x1B,	"Drive 1 Current LimRange Max", "",    DT_F32,     ACCESS_RO,  Save_None,  &pSysShareData->AxisMc[0].sMcSetPara.sCurrentLimRange.Maxinum,            -1000,          10000,      "","",0,0 },
	entry5010[28] = { 0x1C,	"Drive 1 Current LimRange Min", "",    DT_F32,     ACCESS_RO,  Save_None,  &pSysShareData->AxisMc[0].sMcSetPara.sCurrentLimRange.Mininum,            -1000,          10000,      "","",0,0 },

	/*Key  **  Name  **********************, Alias* DataType * ObjAccess ****  pVar  ****************************************************  Minimum  ******  Maximum ***/
//entry5010[29] = { 0x20,	"Drive 2  Info",                "",    DT_None,    ACCESS_RO,  Save_None,  0,                                                           0,              0,      "","",0,0 };
//entry5010[30] = { 0x21,	"Drive 2 Actived",              "",    DT_Bool,    ACCESS_RO,  Save_User,&pSysShareData->AxisMc[0].bActived,                                   0,              1,      "","",0,0 };
//entry5010[31] = { 0x22,	"Drive 2 Name",                 "",    DT_Str,     ACCESS_RO,  Save_User,  &pSysShareData->AxisMc[0].szAxisName,                                 0,              10000,      "","",0,0 };
};

//驱动器1 的参数
SDOEntryDesc entry5011[69] =
{
	/*************** Key  **  Name  **********************, Alias* DataType * ObjAccess *********************  pVar  *****************************************************************************  Minimum  ******  Maximum ***/
	entry5011[0] = { 0x00,	"ServoMonitor",                 "",    DT_None,    ACCESS_RO,  Save_None,  0,                                                                                              0,              0,      "","",0,0 },
	entry5011[1] = { 0x01,	"Motor bActived",               "",    DT_Bool,    ACCESS_RW,  Save_User,   &pSysShareData->AxisMc[0].sMcSetPara.bActived,                                                 0,              1,      "","",0,0 },
	entry5011[2] = { 0x02,	"Sensor Type",                  "",    DT_U8,      ACCESS_RW,  Save_User,    &pSysShareData->AxisMc[0].sMcSetPara.SensorType,                                              0,            255,         "","EnumList#0:UnSet;1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",0,0 },
	entry5011[3] = { 0x03,	"Motor Name",                   "",    DT_Str,     ACCESS_RW,  Save_User,   &pSysShareData->AxisMc[0].sMcSetPara.SensorName,                                               0,              0,      "","",0,0 },
	entry5011[4] = { 0x04,	"Unit",                         "",    DT_U8,      ACCESS_RW,  Save_User,    &pSysShareData->AxisMc[0].sMcSetPara.eUnit,                                                   0,            255,      "","EnumList#1:MM;2:M;3:Inch;11:Deg;12:Min;13:Sec;21:N;22:KN;23:Kgf;24:Gram;31:N_m;32:kgf_m;33:mN_m",0,0 },
	entry5011[5] = { 0x05,	"Display Format",               "",    DT_U8,      ACCESS_RW,  Save_User,    &pSysShareData->AxisMc[0].sMcSetPara.DisplayFormat,                                           0,            100,      "","EnumList#0:xx;1:xx.x;2:xx.xx;3:xx.xxx;4:xx.xxxx;5:xx.xxxxx",0,0 },
	entry5011[6] = { 0x06,	"LogicPos Tare",                "",    DT_Bool,    ACCESS_RW,  Save_None,   &pSysShareData->AxisMc[0].sMcSetPara.bLogicPosTare,                                            0,              1,      "","",0,0 },
	entry5011[7] = { 0x07,	"LogicPos Offset Var",          "",    DT_F32,     ACCESS_RW,  Save_User,   &pSysShareData->AxisMc[0].Axis.Cfg.LogicPositionOffset,                               -100000000,       10000000,      "","",0,0 },

	//entry5011[7] ={0x10,	"Filter0 Type",                 "",    DT_U8,      ACCESS_RW,  Save_User,   &pSysShareData->AxisMc[0].sMcSetPara.FilterData[0].eFilterType,                   0,              1,      "","",0,0},
	//entry5011[7] ={0x11,	"Filter0 Var",                  "",    DT_F32,     ACCESS_RW,  Save_User,   &pSysShareData->AxisMc[0].sMcSetPara.FilterData[0].uFilterSetData.MoveAvgData.iActualCount,                            0.1,            100,      "","",0,0},
	//entry5011[7] ={0x12,	"Filter1 Type",                 "",    DT_U8,      ACCESS_RW,  Save_User,   &pSysShareData->AxisMc[0].sMcSetPara.FilterData[1].eFilterType,                   0,              1,      "","",0,0 },
	//entry5011[7] ={0x13,	"Filter1 Var",                  "",    DT_F32,     ACCESS_RW,  Save_User,   &pSysShareData->AxisMc[0].sMcSetPara.FilterData[1]uFilterSetData.,                            0.1,            100,      "","",0,0 },  
	entry5011[8] = { 0x20,	"Home Position",                "",    DT_F32,     ACCESS_RW,  Save_User,   &pSysShareData->AxisMc[0].sMcSetPara.fHomePosition,                                         -10000,          10000,      "","",0,0 },
	entry5011[9] = { 0x21,	"Reference  Position",          "",    DT_F32,     ACCESS_RW,  Save_User,   &pSysShareData->AxisMc[0].sMcSetPara.fRefPosition,                                          -10000,          10000,      "","",0,0 },
	entry5011[10] = { 0x30,	"Pos Out Range Watch",          "",    DT_Bool,    ACCESS_RW,  Save_User,   &pSysShareData->AxisMc[0].sMcSetPara.sPosLimRange.bWatchOutRange,                               0,              1,      "","",0,0 },
	entry5011[11] = { 0x31,	"Pos Out Range Factor",          "",   DT_F32,     ACCESS_RW,  Save_User,   &pSysShareData->AxisMc[0].sMcSetPara.sPosLimRange.fOutRangeFactor,                            0.1,            100,      "","",0,0 },
	entry5011[12] = { 0x32,	"Pos LimRange Max",             "",    DT_F32,     ACCESS_RW,  Save_User,   &pSysShareData->AxisMc[0].sMcSetPara.sPosLimRange.Maxinum ,                                 -1000,          10000,      "","",0,0 },
	entry5011[13] = { 0x33,	"Pos LimRange Min",             "",    DT_F32,     ACCESS_RW,  Save_User,   &pSysShareData->AxisMc[0].sMcSetPara.sPosLimRange.Mininum,                                  -1000,          10000,      "","",0,0 },
	entry5011[14] = { 0x40,	"Vel Out Range Watch",          "",    DT_Bool,    ACCESS_RW,  Save_User,   &pSysShareData->AxisMc[0].sMcSetPara.sVelLimRange.bWatchOutRange,                              0,              1,      "","",0,0 },
	entry5011[15] = { 0x41,	"Vel Out Range Factor",          "",   DT_F32,     ACCESS_RW,  Save_User,   &pSysShareData->AxisMc[0].sMcSetPara.sVelLimRange.fOutRangeFactor,                            0.1,            100,      "","",0,0 },
	entry5011[16] = { 0x42,	"Vel LimRange Max",             "",    DT_F32,     ACCESS_RW,  Save_User,   &pSysShareData->AxisMc[0].sMcSetPara.sVelLimRange.Maxinum,                                  -1000,          10000,      "","",0,0 },
	entry5011[17] = { 0x43,	"Vel LimRange Min",             "",    DT_F32,     ACCESS_RW,  Save_User,   &pSysShareData->AxisMc[0].sMcSetPara.sVelLimRange.Mininum,                                  -1000,          10000,      "","",0,0 },

	entry5011[18] = { 0x50,	"Acc Out Range Watch",          "",    DT_Bool,    ACCESS_RW,  Save_User,   &pSysShareData->AxisMc[0].sMcSetPara.sAccDecLimRange.bWatchOutRange,                            0,              1,      "","",0,0 },
	entry5011[19] = { 0x51,	"Acc Out Range Factor",          "",   DT_F32,     ACCESS_RW,  Save_User,   &pSysShareData->AxisMc[0].sMcSetPara.sAccDecLimRange.fOutRangeFactor,                          0.1,            100,      "","",0,0 },
	entry5011[20] = { 0x52,	"Acc LimRange Max",             "",    DT_F32,     ACCESS_RW,  Save_User,   &pSysShareData->AxisMc[0].sMcSetPara.sAccDecLimRange.Maxinum,                               -1000,          10000,      "","",0,0 },
	entry5011[21] = { 0x53,	"Acc LimRange Min",             "",    DT_F32,     ACCESS_RW,  Save_User,   &pSysShareData->AxisMc[0].sMcSetPara.sAccDecLimRange.Mininum,                               -1000,          10000,      "","",0,0 },

	entry5011[22] = { 0x60,	"Torque Out Range Watch",       "",    DT_Bool,    ACCESS_RW,  Save_User,   &pSysShareData->AxisMc[0].sMcSetPara.sTorqueLimRange.bWatchOutRange,                           0,              1,      "","",0,0 },
	entry5011[23] = { 0x61,	"Torque Out Range Factor",      "",    DT_F32,     ACCESS_RW,  Save_User,   &pSysShareData->AxisMc[0].sMcSetPara.sTorqueLimRange.fOutRangeFactor,                         0.1,            100,      "","",0,0 },
	entry5011[24] = { 0x62,	"Torque LimRange Max",          "",    DT_F32,     ACCESS_RO,  Save_None,   &pSysShareData->AxisMc[0].sMcSetPara.sTorqueLimRange.Maxinum,                               -1000,          10000,      "","",0,0 },
	entry5011[25] = { 0x63,	"Torque LimRange Min",          "",    DT_F32,     ACCESS_RO,  Save_None,   &pSysShareData->AxisMc[0].sMcSetPara.sTorqueLimRange.Mininum,                               -1000,          10000,      "","",0,0 },
	entry5011[26] = { 0x68,	"Current Out Range Watch",      "",    DT_Bool,    ACCESS_RW,  Save_User,   &pSysShareData->AxisMc[0].sMcSetPara.sCurrentLimRange.bWatchOutRange,                           0,              1,      "","",0,0 },
	entry5011[27] = { 0x69,	"Current Out Range Factor",     "",    DT_F32,     ACCESS_RW,  Save_User,   &pSysShareData->AxisMc[0].sMcSetPara.sCurrentLimRange.fOutRangeFactor,                        0.1,            100,      "","",0,0 },
	entry5011[28] = { 0x6A,	"Current LimRange Max",         "",    DT_F32,     ACCESS_RO,  Save_None,   &pSysShareData->AxisMc[0].sMcSetPara.sCurrentLimRange.Maxinum,                              -1000,          10000,      "","",0,0 },
	entry5011[29] = { 0x6B,	"Current LimRange Min",         "",    DT_F32,     ACCESS_RO,  Save_None,   &pSysShareData->AxisMc[0].sMcSetPara.sCurrentLimRange.Mininum,                              -1000,          10000,      "","",0,0 },

	//编码器精度
	entry5011[30] = { 0x80,	"Motor Pluse PerRevolution",    "",     DT_U32,     ACCESS_RO,   Save_User,  &pSysShareData->AxisMc[0].Axis.Cfg.MotorPlusePerRevolution,                                    0,              0,      "","",0,0 },
	//轴每转一圈的 数值
	entry5011[31] = { 0x81,	"Shaft Pluse PerRevolution",    "",     DT_U32,     ACCESS_RO,   Save_User,  &pSysShareData->AxisMc[0].Axis.Cfg.ShaftPlusePerRevolution,                                    0,              0,      "","",0,0 },
	// 导程 减速比      
	entry5011[32] = { 0x82, "Lead Reduction Ratio",          "",    DT_F32,     ACCESS_RW,  Save_User,  &pSysShareData->AxisMc[0].Axis.Cfg.Tranlatepara,                                            -100,              100,      "", "",   0, 0 },

	entry5011[33] = { 0xA0,	"Servo CW",                     "",     DT_U16,     ACCESS_RO,   Save_None,  &pSysShareData->AxisMc[0].Axis.Motor.LinkVar.CW ,                                              0,              0,      "","",0,0 },
	entry5011[34] = { 0xA2,	"Servo SW",                     "",     DT_U16,     ACCESS_RO,   Save_None,  &pSysShareData->AxisMc[0].Axis.Motor.LinkVar.SW ,                                              0,              0,      "","",0,0 },
	entry5011[35] = { 0xA3,	"Servo Mode Of OpDis",          "",     DT_U8,      ACCESS_RO,   Save_None,  &pSysShareData->AxisMc[0].Axis.Motor.LinkVar.ModeOfOpDis ,                                     0,              255,      "","EnumList#8:CSP;9:CSV;10:CST",0,0 },
	entry5011[36] = { 0xA4,	"Servo bError Status",          "",     DT_Bool,    ACCESS_RO,   Save_None,  &pSysShareData->AxisMc[0].Axis.Motor.bIsError,  		                                        0,	        	0,		"","" },
	entry5011[37] = { 0xA5,	"Servo Error Code",              "",    DT_U16,     ACCESS_RO,   Save_None,  &pSysShareData->AxisMc[0].Axis.Motor.LinkVar.ErrCode ,                                         0,              65535,      "","",0,0 },
	entry5011[38] = { 0xA6,	"Servo Power Up",                 "",   DT_Bool,    ACCESS_RO,   Save_None,  &pSysShareData->AxisMc[0].Axis.Motor.bIsPowerOn,  	                                            0,	        	0,		"","" },

	entry5011[39] = { 0xB2,	"Servo Rated  Current",         "",      DT_F32,      ACCESS_RO,   Save_None,  &pSysShareData->AxisMc[0].Axis.Cfg.RatedCurrent ,                            0.000001,           1000000,      "","",0,0 },
	entry5011[40] = { 0xB4,	"CSV Pos Plan Kp",               "",     DT_F32,     ACCESS_RW,   Save_User,  &pSysShareData->AxisMc[0].Axis.Context.posPidCtrl.kp ,                        0.000001,              1000000,      "","",0,0 },
	entry5011[41] = { 0xB5,	"CSV Pos Plan Pos",              "",     DT_F32,     ACCESS_RO,   Save_None,  &pSysShareData->AxisMc[0].Axis.Context.trapezoidal.step_.Y ,                         0,              0,      "","",0,0 },
	entry5011[42] = { 0xB6,	"Servo Busy",                    "",     DT_Bool,    ACCESS_RO,   Save_None,  &pSysShareData->AxisMc[0].Axis.Busy,                                                          0,              0,      "","",0,0 },
	entry5011[43] = { 0xB7,	"CSV Pos Allow minimum PosErr",  "",     DT_F32,     ACCESS_RW,   Save_User,  &pSysShareData->AxisMc[0].Axis.Context.posPidCtrl.fMinAllowPosErr,               0.001,              10,      "","",0,0 },
	entry5011[44] = { 0xB8,	"CSV Pos feedForwardRatio",  "",         DT_F32,     ACCESS_RW,   Save_User,  &pSysShareData->AxisMc[0].Axis.Context.posPidCtrl.feedForwardRatio,               0.01,              1,      "","",0,0 },
	entry5011[45] = { 0xB9,	"CSV Pos Cmd Delay Counter",     "",     DT_U64,     ACCESS_RO,   Save_None,  &pSysShareData->AxisMc[0].Axis.Context.posPidCtrl.luCmdExeDelayCounter,               0,    0xFFFFFFF,      "","",0,0 },
	entry5011[46] = { 0xBA,	"Plan Done",                     "",     DT_Bool,    ACCESS_RO,   Save_None,  &pSysShareData->AxisMc[0].Axis.Context.trapezoidal.Done,                              0,            1,      "","",0,0 },
	entry5011[47] = { 0xBB,	"Plan Pos Is Ok",                "",     DT_Bool,    ACCESS_RO,   Save_None,  &bPosIsok,                                                                                     0,            1,      "","",0,0 },

	entry5011[48] = { 0xC0,	"Servo Fbk Pos",                 "",     DT_I32,     ACCESS_RO,   Save_None,  &pSysShareData->AxisMc[0].Axis.Motor.LinkVar.PosFbk ,                                         0,              0,      "","",0,0 },
	entry5011[49] = { 0xC1,	"Servo Fbk Vel",                 "",     DT_I32,     ACCESS_RO,   Save_None,  &pSysShareData->AxisMc[0].Axis.Motor.LinkVar.VelFbk ,                                         0,              0,      "","",0,0 },
	entry5011[50] = { 0xC2,	"Servo Fbk Current",             "",     DT_I16,     ACCESS_RO,   Save_None,  &pSysShareData->AxisMc[0].Axis.Motor.LinkVar.CurrentFbk ,                                     0,              0,      "","",0,0 },
	entry5011[51] = { 0xC3,	"Servo Fbk Torque Percent",      "",     DT_I16,     ACCESS_RO,   Save_None,  &pSysShareData->AxisMc[0].Axis.Motor.LinkVar.TorqueFbk_Percent ,                                      0,           4000,      "","",0,0 },
	entry5011[52] = { 0xC4,	"Servo Rated Torque",            "",     DT_F32,     ACCESS_RO,   Save_None,  &pSysShareData->AxisMc[0].Axis.Motor.fMotorRatedTorque ,                                      0,           4000,      "","",0,0 },

	entry5011[53] = { 0xC8,	"Servo Ref Pos",                 "",     DT_I32,     ACCESS_RO,   Save_None,  &pSysShareData->AxisMc[0].Axis.Motor.LinkVar.PosRef ,                                         0,              0,      "","",0,0 },
	entry5011[54] = { 0xC9,	"Servo Ref Vel",                 "",     DT_I32,     ACCESS_RO,   Save_None,  &pSysShareData->AxisMc[0].Axis.Motor.LinkVar.VelRef ,                                         0,              0,      "","",0,0 },
	entry5011[55] = { 0xCA,	"Servo Ref Current ",            "",     DT_I16,     ACCESS_RO,   Save_None,  &pSysShareData->AxisMc[0].Axis.Motor.LinkVar.CurrentRef,                                  -3000,              3000,      "","",0,0 },
	entry5011[56] = { 0xCB,	"Servo Ref Torque ",              "",    DT_I16,     ACCESS_RO,   Save_None,  &pSysShareData->AxisMc[0].Axis.Motor.LinkVar.TorqueRef,                                   -3000,              3000,      "","",0,0 },
	entry5011[57] = { 0xCC,	"Servo Pos Error",               "",     DT_I32,     ACCESS_RO,   Save_None,  &pSysShareData->AxisMc[0].Axis.Status.iPosError ,                                             0,              0,      "","",0,0 },


	entry5011[58] = { 0xD0,	"Logic Fbk Pos",                 "",     DT_F32,     ACCESS_RO,   Save_None,  &pSysShareData->AxisMc[0].Axis.LogicFbk.fPos,                                                 0,              0,      "","",0,0 },
	entry5011[59] = { 0xD1,	"Logic Fbk Vel",                 "",     DT_F32,     ACCESS_RO,   Save_None,  &pSysShareData->AxisMc[0].Axis.LogicFbk.fVel,                                                 0,              0,      "","",0,0 },
	entry5011[60] = { 0xD2,	"Logic Fbk Acc",                 "",     DT_F32,     ACCESS_RO,   Save_None,  &pSysShareData->AxisMc[0].Axis.LogicFbk.fAcc,                                                 0,              0,      "","",0,0 },
	entry5011[61] = { 0xD3,	"Logic Pos Error",               "",     DT_F32,     ACCESS_RO,   Save_None,  &pSysShareData->AxisMc[0].Axis.Status.fPosError ,                                             0,              0,      "","",0,0 },
	entry5011[62] = { 0xD4,	"Logic Fbk Torque",              "",     DT_F32,     ACCESS_RO,   Save_None,  &pSysShareData->AxisMc[0].Axis.LogicFbk.fTorque ,                                             0,              0,      "","",0,0 },
	entry5011[63] = { 0xD5,	"Logic Fbk Current",             "",     DT_F32,     ACCESS_RO,   Save_None,  &pSysShareData->AxisMc[0].Axis.LogicFbk.fCurrent,                                             0,              0,      "","",0,0 },

	entry5011[64] = { 0xD8,	"Logic Ref Pos",                 "",     DT_F32,     ACCESS_RO,   Save_None,  &pSysShareData->AxisMc[0].Axis.LogicRef.fPos,                                                 0,              0,      "","",0,0 },
	entry5011[65] = { 0xD9,	"Logic Ref Vel",                 "",     DT_F32,     ACCESS_RO,   Save_None,  &pSysShareData->AxisMc[0].Axis.LogicRef.fVel,                                                 0,              0,      "","",0,0 },
	entry5011[66] = { 0xDA,	"Logic Ref Acc",                 "",     DT_F32,     ACCESS_RO,   Save_None,  &pSysShareData->AxisMc[0].Axis.LogicRef.fAcc,                                                 0,              0,      "","",0,0 },
	entry5011[67] = { 0xF0,	"Abort Plan Counter",            "",     DT_U64,     ACCESS_RO,   Save_None,  &pSysShareData->sTestData.gAbortCounter,                                                      0,              0,      "","",0,0 },
	entry5011[68] = { 0xF1,	"Plan Ret",                      "",     DT_I16,     ACCESS_RO,   Save_None,  &pSysShareData->sTestData.gPlanRet,                                                           0,              0,      "","",0,0 }
};


//传感器  力总览信息
SDOEntryDesc entry5050[10] = {
	/*Key  **  Name  **********************, Alias* DataType * ObjAccess ****  pVar  ***************************************************************  Minimum  ******  Maximum ***/
	entry5050[0] = { 0x0,	"System Sensor Force",          "",    DT_None,    ACCESS_RO,  Save_None,  0,                                                                0,                 0,      "","",0,0 },
	entry5050[1] = { 0x1,	"Force 1 Actived",              "",    DT_Bool,    ACCESS_RO,  Save_User,  &pSysShareData->sForceData[0].bActived,                                          0,                 1,      "","",0,0 },
	entry5050[2] = { 0x2,	"Force 1 Name",                 "",    DT_Str,     ACCESS_RO,  Save_User,  &pSysShareData->sForceData[0].SensorName,                                        0,             10000,      "","",0,0 },
	entry5050[3] = { 0x3,	"Force 1 Type",                 "",    DT_U8,      ACCESS_RO,  Save_User,  &pSysShareData->sForceData[0].SensorType,                                        0,             255,      "","EnumList#1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",0,0 },
	entry5050[4] = { 0x4,	"Force 1 Cal NoFiltr Var",      "",    DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->sForceData[0].fSensorCalibratedVar,                              0,             10000,      "","",0,0 },
	entry5050[5] = { 0x5,	"Force 1 CalibratedVar",        "",    DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->sForceData[0].fSensorCalibratedVar,                              0,             10000,      "","",0,0 },
	entry5050[6] = { 0x6,	"Force 1 Watch Out Range",     "",     DT_Bool,    ACCESS_RO,  Save_User,  &pSysShareData->sForceData[0].sLimRange.bWatchOutRange,                           0,                 1,      "","",0,0 },
	entry5050[7] = { 0x7,	"Force 1 Out Range Factor",     "",    DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->sForceData[0].sLimRange.fOutRangeFactor,                        0.1,              100,      "","",0,0 },
	entry5050[8] = { 0x8,	"Force 1 LimRange Maxinum",     "",    DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->sForceData[0].sLimRange.Maxinum,                              -1000,            10000,      "","",0,0 },
	entry5050[9] = { 0x9,	"Force 1 LimRange Mininum",     "",    DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->sForceData[0].sLimRange.Mininum,                              -1000,            10000,      "","",0,0 }
};

//传感器  Force Sensor1
SDOEntryDesc entry5051[29] =
{
	/**************** Key  **  Name  **********************, Alias* DataType * ObjAccess ****  pVar  **************************************************************  Minimum  ******  Maximum ***/
	entry5051[0] = { 0x0,	"Force 1",                     "",    DT_None,    ACCESS_RO,  Save_None,  0,                                                                0,              0,      "","",0,0 },
		entry5051[1] = { 0x1,	"Sensor Actived",              "",    DT_Bool,    ACCESS_RW,  Save_User,  &pSysShareData->sForceData[0].bActived,                                          0,              1,      "","",0,0 },
		entry5051[2] = { 0x2,	"Sensor Name",                 "",    DT_Str,     ACCESS_RW,  Save_User,  &pSysShareData->sForceData[0].SensorName,                                        0,              10000,      "","",0,0 },
		entry5051[3] = { 0x3,	"Sensor Type",                 "",    DT_U8,      ACCESS_RW,  Save_User,  &pSysShareData->sForceData[0].SensorType,                                         0,              255,      "","EnumList#1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",0,0 },
		entry5051[4] = { 0x4,	"Sensor Unit",                 "",    DT_U8,      ACCESS_RW,  Save_User,  &pSysShareData->sForceData[0].eUnit,                                             0,              255,      "","EnumList#0:NONE;1:MM;2:M;3:Inch;11:Deg;12:Min;13:Sec;21:N;22:KN;23:Kgf;24:Gram;31:N_m;32:kgf_m;33:mN_m",0,0 },
		entry5051[5] = { 0x5,	"Sensor DisplayFormat",        "",    DT_U8,      ACCESS_RW,  Save_User,  &pSysShareData->sForceData[0].DisplayFormat,                                     0,              255,      "","EnumList#0:xx;1:xx.x;2:xx.xx;3:xx.xxx;4:xx.xxxx;5:xx.xxxxx",0,0 },
		entry5051[6] = { 0x6,	"Sensor OriginalVar",          "",    DT_I32,     ACCESS_RO,  Save_User,  &pSysShareData->sForceData[0].iSensorOriginalVar,                                0,              10000,      "","",0,0 },
		entry5051[7] = { 0x7,	"Sensor CalibratedVar",        "",    DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->sForceData[0].fSensorCalibratedVar,                              0,              10000,      "","",0,0 },

		entry5051[8] = { 0x8,	"Sensor bTare",                "",    DT_Bool,    ACCESS_RW,  Save_None,  &pSysShareData->sForceData[0].bTare,                                             0,              10000,      "","",0,0 },
		entry5051[9] = { 0x9,	"Sensor Range",                "",    DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->sForceData[0].sCalibrateData.fSensorRange,                   -1000,          10000,      "","",0,0 },
		entry5051[10] = { 0xA,	"Sensor MultiPointCalCount",   "",    DT_U8,      ACCESS_RO,  Save_User,  &pSysShareData->sForceData[0].sCalibrateData.MultiPointCalCount,                 0,              20,      "","",0,0 },
		entry5051[11] = { 0xB,	"Sensor fB",                   "",    DT_F32,     ACCESS_RW,  Save_User,  &pSysShareData->sForceData[0].sCalibrateData.fSensB,                     -10000000,            1000,      "","",0,0 },
		entry5051[12] = { 0xC,	"Sensor Var OffSet",           "",    DT_F32,     ACCESS_RW,  Save_User,  &pSysShareData->sForceData[0].sCalibrateData.fSensC,                     -10000000,            1000,      "","",0,0 },
		entry5051[13] = { 0xD,	"Sensor Calibrate Type",       "",    DT_U8,      ACCESS_RO,  Save_User,  &pSysShareData->sForceData[0].sCalibrateData.eCalibrateType,                     0,               1,      "","EnumList#0:SensitivityCoe;1:MultiPoint",0,0 },
		entry5051[14] = { 0xE,	"Sensor Sensitivity Coe ",     "",    DT_F32,     ACCESS_RO,  Save_None,  &pSysShareData->sForceData[0].sCalibrateData.fSensCoe,                            0.01,           100,      "","",0,0 },

		entry5051[15] = { 0x20,	"Sensor Watch Out Range",      "",    DT_Bool,    ACCESS_RW,  Save_User,  &pSysShareData->sForceData[0].sLimRange.bWatchOutRange,                           0,                 1,      "","",0,0 },
		entry5051[16] = { 0x21,	"Sensor Out Range Factor",     "",    DT_F32,     ACCESS_RW,  Save_User,  &pSysShareData->sForceData[0].sLimRange.fOutRangeFactor,                        0.1,              100,      "","",0,0 },
		entry5051[17] = { 0x22,	"Sensor LimRange Maxinum",     "",    DT_F32,     ACCESS_RW,  Save_User,  &pSysShareData->sForceData[0].sLimRange.Maxinum,                                 -1000,          10000,      "","",0,0 },
		entry5051[18] = { 0x23,	"Sensor LimRange Mininum ",    "",    DT_F32,     ACCESS_RW,  Save_User,  &pSysShareData->sForceData[0].sLimRange.Mininum,                                 -1000,          10000,      "","",0,0 },

		entry5051[19] = { 0x30,	"Sensor fSensCoe00",            "",    DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->sForceData[0].sCalibrateData.CalibrateData[0].fSensCoe,          -1000,          10000,      "","",0,0 },
		entry5051[20] = { 0x31,	"Sensor fSensCoe01",            "",    DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->sForceData[0].sCalibrateData.CalibrateData[1].fSensCoe,          -1000,          10000,      "","",0,0 },
		entry5051[21] = { 0x32,	"Sensor fSensCoe02",            "",    DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->sForceData[0].sCalibrateData.CalibrateData[2].fSensCoe,          -1000,          10000,      "","",0,0 },
		entry5051[22] = { 0x33,	"Sensor fSensCoe03",            "",    DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->sForceData[0].sCalibrateData.CalibrateData[3].fSensCoe,          -1000,          10000,      "","",0,0 },
		entry5051[23] = { 0x34,	"Sensor fSensCoe04",            "",    DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->sForceData[0].sCalibrateData.CalibrateData[4].fSensCoe,          -1000,          10000,      "","",0,0 },
		entry5051[24] = { 0x35,	"Sensor fSensCoe05",            "",    DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->sForceData[0].sCalibrateData.CalibrateData[5].fSensCoe,          -1000,          10000,      "","",0,0 },
		entry5051[25] = { 0x36,	"Sensor fSensCoe06",            "",    DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->sForceData[0].sCalibrateData.CalibrateData[6].fSensCoe,          -1000,          10000,      "","",0,0 },
		entry5051[26] = { 0x37,	"Sensor fSensCoe07",            "",    DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->sForceData[0].sCalibrateData.CalibrateData[7].fSensCoe,          -1000,          10000,      "","",0,0 },
		entry5051[27] = { 0x38,	"Sensor fSensCoe08",            "",    DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->sForceData[0].sCalibrateData.CalibrateData[8].fSensCoe,          -1000,          10000,      "","",0,0 },
		entry5051[28] = { 0x39,	"Sensor fSensCoe09",            "",    DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->sForceData[0].sCalibrateData.CalibrateData[9].fSensCoe,          -1000,          10000,      "","",0,0 }
};


//传感器  sExtPosData Sensor1
SDOEntryDesc entry5060[9] =
{
	/*************** Key  **  Name  **********************, Alias* DataType * ObjAccess ****  pVar  ****************************************************  Minimum  ******  Maximum ***/
	entry5060[0] = { 0x0,	"System Sensor ExtPos",         "",     DT_None,    ACCESS_RO,  Save_None,  0,                                                     0,              0,      "","",0,0 },
	entry5060[1] = { 0x1,	"ExtPos 1 Actived",             "",     DT_Bool,    ACCESS_RO,  Save_User,  &pSysShareData->sExtPosData[0].bActived,                                          0,              1,          "","",0,0 },
	entry5060[2] = { 0x2,	"ExtPos 1 Name",                 "",    DT_Str,     ACCESS_RO,  Save_User,  &pSysShareData->sExtPosData[0].SensorName,                                        0,              10000,      "","",0,0 },
	entry5060[3] = { 0x3,	"ExtPos 1 Type",                 "",    DT_U8,      ACCESS_RO,  Save_User,  &pSysShareData->sExtPosData[0].SensorType,                                        0,              255,      "","EnumList#1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",0,0 },
	entry5060[4] = { 0x5,	"ExtPos 1 CalibratedVar",        "",    DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->sExtPosData[0].fSensorCalibratedVar,                              0,              10000,      "","",0,0 },
	entry5060[5] = { 0x6,	"ExtPos 1 Watch Out Range",      "",    DT_Bool,    ACCESS_RO,  Save_User, &pSysShareData->sExtPosData[0].sLimRange.bWatchOutRange,                          0,                   1,      "","",0,0 },
	entry5060[6] = { 0x7,	"ExtPos 1 Out Range Factor",     "",    DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->sExtPosData[0].sLimRange.fOutRangeFactor,                        0.1,               100,      "","",0,0 },
	entry5060[7] = { 0x8,	"ExtPos 1 LimRange Maxinum",     "",    DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->sExtPosData[0].sLimRange.Maxinum,                              -1000,          10000,      "","",0,0 },
	entry5060[8] = { 0x9,	"ExtPos 1 LimRange Mininum",     "",    DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->sExtPosData[0].sLimRange.Mininum,                              -1000,          10000,      "","",0,0 }
};

//传感器  sExtPosData Sensor1
SDOEntryDesc entry5061[29] = {
	/*Key  **  Name  **********************, Alias* DataType * ObjAccess ****  pVar  ****************************************************  Minimum  ******  Maximum ***/
	entry5061[0] = { 0x0,	"sExtPosData 1",               "",    DT_None,    ACCESS_RO,  Save_None,  0,                                                                     0,              0,      "","",0,0 },
	entry5061[1] = { 0x1,	"Sensor Actived",              "",    DT_Bool,    ACCESS_RW,  Save_User,  &pSysShareData->sExtPosData[0].bActived,                                          0,              1,      "","",0,0 },
	entry5061[2] = { 0x2,	"Sensor Name",                 "",    DT_Str,     ACCESS_RW,  Save_User,  &pSysShareData->sExtPosData[0].SensorName,                                        0,              10000,      "","",0,0 },
	entry5061[3] = { 0x3,	"Sensor Type",                 "",    DT_U8,      ACCESS_RW,  Save_User,  &pSysShareData->sExtPosData[0].SensorType,                                        0,              255,      "","EnumList#1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",0,0 },
	entry5061[4] = { 0x4,	"Sensor Unit",                 "",    DT_U8,      ACCESS_RW,  Save_User,  &pSysShareData->sExtPosData[0].eUnit,                                             0,              255,      "","EnumList#0:NONE;1:MM;2:M;3:Inch;11:Deg;12:Min;13:Sec;21:N;22:KN;23:Kgf;24:Gram;31:N_m;32:kgf_m;33:mN_m",0,0 },
	entry5061[5] = { 0x5,	"Sensor DisplayFormat",        "",    DT_U8,      ACCESS_RW,  Save_User,  &pSysShareData->sExtPosData[0].DisplayFormat,                                     0,              255,      "","EnumList#0:xx;1:xx.x;2:xx.xx;3:xx.xxx;4:xx.xxxx;5:xx.xxxxx",0,0 },
	entry5061[6] = { 0x6,	"Sensor OriginalVar",          "",    DT_I32,     ACCESS_RO,  Save_User,  &pSysShareData->sExtPosData[0].iSensorOriginalVar,                               0,              10000,      "","",0,0 },
	entry5061[7] = { 0x7,	"Sensor CalibratedVar",        "",    DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->sExtPosData[0].fSensorCalibratedVar,                              0,              10000,      "","",0,0 },

	entry5061[8] = { 0x8,	"Sensor bTare",                "",    DT_Bool,    ACCESS_RW,  Save_None,  &pSysShareData->sExtPosData[0].bTare,                                             0,              10000,      "","",0,0 },
	entry5061[9] = { 0x9,	"Sensor Range",                "",    DT_F32,     ACCESS_RO,  Save_User,  &pSysShareData->sExtPosData[0].sCalibrateData.fSensorRange,                    -1000,          10000,      "","",0,0 },
	entry5061[10] = { 0xA,	"Sensor MultiPointCalCount",   "",    DT_U8,      ACCESS_RO,  Save_User,  &pSysShareData->sExtPosData[0].sCalibrateData.MultiPointCalCount,                 0,              20,      "","",0,0 },
	entry5061[11] = { 0xB,	"Sensor fB",                   "",    DT_F32,     ACCESS_RW,  Save_User,  &pSysShareData->sExtPosData[0].sCalibrateData.fSensB,                     -10000000,      1000,      "","",0,0 },
	entry5061[12] = { 0xC,	"Sensor Var OffSet",           "",    DT_F32,     ACCESS_RW,  Save_User,  &pSysShareData->sExtPosData[0].sCalibrateData.fSensC,                     -10000000,      1000,      "","",0,0 },
	entry5061[13] = { 0xD,	"Sensor Calibrate Type",       "",    DT_U8,      ACCESS_RO,  Save_User,  &pSysShareData->sExtPosData[0].sCalibrateData.eCalibrateType,                     0,               1,      "","EnumList#0:SensitivityCoe;1:MultiPoint",0,0 },
	entry5061[14] = { 0xE,	"Sensor Sensitivity Coe ",     "",    DT_F32,     ACCESS_RO,  Save_None,  &pSysShareData->sExtPosData[0].sCalibrateData.fSensCoe,                            0.01,           100,      "","",0,0 },

	entry5061[15] = { 0x20,	"Sensor Watch Out Range",      "",    DT_Bool,    ACCESS_RW,  Save_User,  &pSysShareData->sExtPosData[0].sLimRange.bWatchOutRange,                           0,                 1,      "","",0,0 },
	entry5061[16] = { 0x21,	"Sensor Out Range Factor",     "",    DT_F32,     ACCESS_RW,  Save_User,  &pSysShareData->sExtPosData[0].sLimRange.fOutRangeFactor,                        0.1,              100,      "","",0,0 },
	entry5061[17] = { 0x22,	"Sensor LimRange Maxinum",     "",    DT_F32,     ACCESS_RW,  Save_User,  &pSysShareData->sExtPosData[0].sLimRange.Maxinum,                                 -1000,          10000,      "","",0,0 },
	entry5061[18] = { 0x23,	"Sensor LimRange Mininum ",    "",    DT_F32,     ACCESS_RW,  Save_User,  &pSysShareData->sExtPosData[0].sLimRange.Mininum,                                 -1000,          10000,      "","",0,0 },

	entry5061[19] = { 0x30,	"Sensor fSensCoe00",            "",    DT_F32,     ACCESS_RO,  Save_None,  &pSysShareData->sExtPosData[0].sCalibrateData.CalibrateData[0].fSensCoe,          -1000,          10000,      "","",0,0 },
	entry5061[20] = { 0x31,	"Sensor fSensCoe01",            "",    DT_F32,     ACCESS_RO,  Save_None,  &pSysShareData->sExtPosData[0].sCalibrateData.CalibrateData[1].fSensCoe,          -1000,          10000,      "","",0,0 },
	entry5061[21] = { 0x32,	"Sensor fSensCoe02",            "",    DT_F32,     ACCESS_RO,  Save_None,  &pSysShareData->sExtPosData[0].sCalibrateData.CalibrateData[2].fSensCoe,          -1000,          10000,      "","",0,0 },
	entry5061[22] = { 0x33,	"Sensor fSensCoe03",            "",    DT_F32,     ACCESS_RO,  Save_None,  &pSysShareData->sExtPosData[0].sCalibrateData.CalibrateData[3].fSensCoe,          -1000,          10000,      "","",0,0 },
	entry5061[23] = { 0x34,	"Sensor fSensCoe04",            "",    DT_F32,     ACCESS_RO,  Save_None,  &pSysShareData->sExtPosData[0].sCalibrateData.CalibrateData[4].fSensCoe,          -1000,          10000,      "","",0,0 },
	entry5061[24] = { 0x35,	"Sensor fSensCoe05",            "",    DT_F32,     ACCESS_RO,  Save_None,  &pSysShareData->sExtPosData[0].sCalibrateData.CalibrateData[5].fSensCoe,          -1000,          10000,      "","",0,0 },
	entry5061[25] = { 0x36,	"Sensor fSensCoe06",            "",    DT_F32,     ACCESS_RO,  Save_None,  &pSysShareData->sExtPosData[0].sCalibrateData.CalibrateData[6].fSensCoe,          -1000,          10000,      "","",0,0 },
	entry5061[26] = { 0x37,	"Sensor fSensCoe07",            "",    DT_F32,     ACCESS_RO,  Save_None,  &pSysShareData->sExtPosData[0].sCalibrateData.CalibrateData[7].fSensCoe,          -1000,          10000,      "","",0,0 },
	entry5061[27] = { 0x38,	"Sensor fSensCoe08",            "",    DT_F32,     ACCESS_RO,  Save_None,  &pSysShareData->sExtPosData[0].sCalibrateData.CalibrateData[8].fSensCoe,          -1000,          10000,      "","",0,0 },
	entry5061[28] = { 0x39,	"Sensor fSensCoe09",            "",    DT_F32,     ACCESS_RO,  Save_None,  &pSysShareData->sExtPosData[0].sCalibrateData.CalibrateData[9].fSensCoe,          -1000,          10000,      "","",0,0 }
};


//传感器  sSysProtectPara
SDOEntryDesc entry50FF[7] = {
	/*Key  *********************  Name  *************************, Alias* DataType * ObjAccess ****  pVar  *****************************************************************************  Minimum  ******  Maximum ***/
	entry50FF[0] = { 0x0,	"System Protect Para",                  "",    DT_None,    ACCESS_RO,  Save_None,  0,                                                                         0,              0,      "","",0,0 },
	entry50FF[1] = { 0x01,	"Not Use Position Fllow Protect",       "",    DT_Bool,    ACCESS_RW,  Save_User,  &sSysProtectPara.sSysPosPara.bNotUsePosFllowErr,                           0,              1,      "","",0,0 },
	entry50FF[2] = { 0x02,	"Allow FbkPos RefPos Difference value", "",    DT_F32,     ACCESS_RW,  Save_User,  &sSysProtectPara.sSysPosPara.fAllowPosDiffValue,                           0,              20,      "","",0,0 },
	entry50FF[3] = { 0x03,	"Reset Max Pos Difference",             "",    DT_Bool,    ACCESS_RW,  Save_None,  &sSysProtectPara.sSysPosPara.bResetMaxPosDiff,                             0,              1,      "","",0,0 },
	entry50FF[4] = { 0x04,	"Max Pos Difference value",             "",    DT_F32,     ACCESS_RO,  Save_User,  &sSysProtectPara.sSysPosPara.fMaxPosDiff,                                  0,              10000000,"","",0,0 },

	entry50FF[5] = { 0x10,	"Not Use Force Current Protect",        "",    DT_Bool,    ACCESS_RW,  Save_User,  &sSysProtectPara.sSysForceCurrPara.bNotUseCurentForceErr,                  0,              1,      "","",0,0 },
	entry50FF[6] = { 0x11,	"Max Force Ratio ",                     "",    DT_F32,     ACCESS_RW,  Save_User,  &sSysProtectPara.sSysForceCurrPara.fForceRatio,                            0,              1,      "","",0,0 }
};

//运行计数器
SDOEntryDesc entry6000[9] =
{
	/*************** Key  **  Name  **********************, Alias* DataType * ObjAccess ****  pVar  **************************************  Minimum  *****  Maximum ***/
	entry6000[0] = { 0x00,	"System Statistics Data",       "",     DT_None,    ACCESS_RO,  Save_None,  0,                                       0,            0,      "","",0,0 },
	entry6000[1] = { 0x01,	"Reset Global Statistics Data", "",     DT_Bool,    ACCESS_RW,  Save_None,  &sSysStaData.bResetGlobalSta,            0,             1,      "","",0,0 },
	entry6000[2] = { 0x02,	"Global Count",                 "",     DT_U64,     ACCESS_RO,  Save_None,  &sSysStaData.sGlobal.ulCount,            0,     50000000,      "","",0,0 },
	entry6000[3] = { 0x03,	"Global Ok Count",              "",     DT_U64,     ACCESS_RO,  Save_None,  &sSysStaData.sGlobal.ulOkCount,          0,     50000000,      "","",0,0 },
	entry6000[4] = { 0x04,	"Global Nok Count",             "",     DT_U64,     ACCESS_RO,  Save_None,  &sSysStaData.sGlobal.ulNokCount,         0,     50000000,      "","",0,0 },

	/*************** Key  **  Name  **********************, Alias* DataType * ObjAccess ****  pVar  ****************************************************  Minimum  ******  Maximum ***/
	entry6000[5] = { 0x61,	"Reset Profile Statistics Data","",     DT_Bool,    ACCESS_RW,  Save_None,  &sSysStaData.bResetProfleSta,         0,            1,      "","",0,0 },
	entry6000[6] = { 0x62,	"Profile Count",                "",     DT_U64,     ACCESS_RO,  Save_None,  &sSysStaData.sProfile.ulCount ,       0,     50000000,      "","",0,0 },
	entry6000[7] = { 0x63,	"Profile Ok Count",             "",     DT_U64,     ACCESS_RO,  Save_None,  &sSysStaData.sProfile.ulOkCount,      0,     50000000,      "","",0,0 },
	entry6000[8] = { 0x64,	"Profile Nok Count",            "",     DT_U64,     ACCESS_RO,  Save_None,  &sSysStaData.sProfile.ulNokCount,     0,     50000000,      "","",0,0 }
};

//Jog 参数
SDOEntryDesc entry7001[18] = {
	/*Key  **  Name  **********************, Alias* DataType * ObjAccess ****  pVar  *****************************  Minimum  *****  Maximum ***/
	entry7001[0] = { 0x0,	"System JogPara",               "",     DT_None,    ACCESS_RO,  Save_None,  0,                              0,              0,         "","",0,0 },
	entry7001[1] = { 0x01,	"Home Pos",                     "",     DT_F32,     ACCESS_RW,  Save_User,  &pSysShareData->sSysJogPara.fHomePos,          0,          10000,      "","",0,0 },
	entry7001[2] = { 0x03,	"Jog Acc",                      "",     DT_F32,     ACCESS_RW,  Save_User,  &pSysShareData->sSysJogPara.fJogAcc,           1,           5000,      "","",0,0 },
	entry7001[3] = { 0x04,	"VelocityMax",                  "",     DT_F32,     ACCESS_RW,  Save_User,  &pSysShareData->sSysJogPara.fVelocityMax,      0,           1000,      "","",0,0 },
	entry7001[4] = { 0x05,	"Velocity",                     "",     DT_F32,     ACCESS_RW,  Save_User,  &pSysShareData->sSysJogPara.fVelocity,      0.01,             40,      "","",0,0 },
	entry7001[5] = { 0x06,	"StepDistance",                 "",     DT_F32,     ACCESS_RW,  Save_User,  &pSysShareData->sSysJogPara.fStepDistance,  0.01,              5,      "","",0,0 },
	entry7001[6] = { 0x07,	"Target Channel Name",          "",     DT_Str,     ACCESS_RW,  Save_User,  &pSysShareData->sSysJogPara.sCustomChannelName,0.01,           1,      "","",0,0 },
	entry7001[7] = { 0x08,	"Target Var",                   "",     DT_F32,     ACCESS_RW,  Save_User,  &pSysShareData->sSysJogPara.fCustomVar,     -100000,     1000000,      "","",0,0 },
	entry7001[8] = { 0x09,	"Cmd Jog TargetVar",             "",    DT_Bool,    ACCESS_RW,  Save_User,  &pSysShareData->sSysJogPara.bJogCustomCmd,     0,              1,      "","",0,0 },
	entry7001[9] = { 0x0A,	"eJogMoveMode",                 "",     DT_I16,     ACCESS_RW,  Save_User,  &pSysShareData->sSysJogPara.eJogMoveMode,      0,              1,      "","EnumList#0:VelocityMode;1:PosMode",0,0 },
	entry7001[10] = { 0x10,	"Position Max",                 "",     DT_F32,     ACCESS_RW,  Save_User,  &pSysShareData->sSysJogPara.fPositionMax,   -100000,     1000000,      "","",0,0 },
	entry7001[11] = { 0x11,	"Position Min",                 "",     DT_F32,     ACCESS_RW,  Save_User,  &pSysShareData->sSysJogPara.fPositionMin,   -100000,     1000000,      "","",0,0 },
	entry7001[12] = { 0x12,	"Force Max",                    "",     DT_F32,     ACCESS_RW,  Save_User,  &pSysShareData->sSysJogPara.fForceMax,      -100000,     1000000,      "","",0,0 },
	entry7001[13] = { 0x13,	"Force Min",                    "",     DT_F32,     ACCESS_RW,  Save_User,  &pSysShareData->sSysJogPara.fForceMin,      -100000,     1000000,      "","",0,0 },
	entry7001[14] = { 0x14,	"ExtPos Max",                   "",     DT_F32,     ACCESS_RW,  Save_User,  &pSysShareData->sSysJogPara.fExtPosMax,     -100000,     1000000,      "","",0,0 },
	entry7001[15] = { 0x15,	"ExtPos Min",                   "",     DT_F32,     ACCESS_RW,  Save_User,  &pSysShareData->sSysJogPara.fExtPosMin,     -100000,     1000000,      "","",0,0 },
	entry7001[16] = { 0x20,	"Profile Home JogCmd ",         "",     DT_Bool,    ACCESS_RW,  Save_None,  &pSysShareData->sSysJogPara.bProfileHomeJogCmd,  0,              1,      "","",0,0 },
	entry7001[17] = { 0x21,	"Profile Home Jog Velocity",    "",     DT_F32,     ACCESS_RO,  Save_None,  &pSysShareData->sSysJogPara.fProfileHomeVelocity,  0,     10000000,      "","",0,0 }
};

//Sequence 参数
SDOEntryDesc entry8001[5] = {
	/**************** Key  **  Name  **********************, Alias* DataType * ObjAccess ****  pVar  *****************************  Minimum  *****  Maximum ***/
	entry8001[0] = { 0x0,	"Sequence Para",               "",     DT_None,    ACCESS_RO,  Save_None,  0,                              0,            0,         "","",0,0 },
	entry8001[1] = { 0x01,	"Active Row Index",            "",     DT_I16,     ACCESS_RO,  Save_None,  &pSysShareData->SeqAux.iActiveRowIndex,          -1,          255,      "","",0,0 },
	entry8001[2] = { 0x02,	"Error  Row Index",            "",     DT_I16,     ACCESS_RO,  Save_None,  &pSysShareData->SeqAux.iErrorRowIndex,           -1,          255,      "","",0,0 },
	entry8001[3] = { 0x10,	"Move Delay Exit Time ms",     "",     DT_F32,     ACCESS_RW,  Save_User,  &pSysShareData->SeqAux.sMotionAux.fMoveDelayExitTime_ms,    0,      1000,      "","",0,0 },
	entry8001[4] = { 0x20,	"SlopTrig fSlop",              "",     DT_F32,     ACCESS_RO,  Save_None,  &pSysShareData->SeqAux.sMotionAux.fSlop,         0,      1000,      "","",0,0 }
};

//SDOEntryDesc entry8002[3]={
//    /**************** Key  **  Name  *************************************, Alias* DataType * ObjAccess **************************************  pVar  *****************************  Minimum  *****  Maximum ***/
//    entry8002[0] = { 0x0,	"Sequence Sensor Loop Para",                    "",     DT_None,    ACCESS_RO,  Save_None,                                                   0,          0,            0,      "","",0,0 },
//    entry8002[1] = { 0x01,	"Loop OK Close Percent Relative to Target",     "",     DT_F32,     ACCESS_RW,  Save_User,    &pSysShareData->SeqAux.sLoopCtrlData.LoopOKClosePercentRelative2Target,       0.5,           0.99,      "","",0,0 },
//    entry8002[2] = { 0x02,	"Loop OK Close Percent Relative to FullScale",  "",     DT_F32,     ACCESS_RW,  Save_User,    &pSysShareData->SeqAux.sLoopCtrlData.LoopOKClosePercentRelative2FullScale,       0.01,          0.99,      "","",0,0 }
//};

SDOEntryDesc entry9001[27] = {
	/*************** Key  **  Name  **********************, Alias* DataType * ObjAccess ****  pVar  **********************************************************************************  Minimum  *****  Maximum ***/
	entry9001[0] = { 0x0,	"Sn Para",                      "",    DT_None,    ACCESS_RO,  Save_None,  0,                                                                                   0,              0,      "","",0,0 },
	entry9001[1] = { 0x1,	"sSn",                          "",    DT_Str,     ACCESS_RO,  Save_None,  &gSysSnData.sSn,                                                       0,          10000,      "","",0,0 },
	entry9001[2] = { 0x2,	"Global Sn Counter",            "",    DT_U64,     ACCESS_RO,  Save_None,  &gSysSnData.GlobalSnCounter,                                           0,      (uint64)4294967295,      "","",0,0 },
	entry9001[3] = { 0x4,	"Reset Global Counter",         "",    DT_Bool,    ACCESS_RW,  Save_None,  &gSysSnData.bResetCounter,                                             0,               1,      "","",0,0 },

	entry9001[4] = {0x5,	"Illegal Char SwitchBlank",      "",   DT_Bool,    ACCESS_RO,  Save_None,  &gSysSnData.GlobalSnPara.sPlcSnSetPara.bIllegalCharSwitchBlank,        0,               1,      "","",0,0},
	entry9001[5] = {0x6,	"Same Sn BingTime",             "",    DT_Bool,    ACCESS_RO,  Save_None,  &gSysSnData.GlobalSnPara.sPlcSnSetPara.bSameSnBingTime ,               0,               1,      "","",0,0},
	entry9001[6] = {0x7,	"Null Sn Error",                "",    DT_Bool,    ACCESS_RO,  Save_None,  &gSysSnData.GlobalSnPara.sPlcSnSetPara.bNullSnError,                   0,               1,      "","",0,0},
	entry9001[7] = {0x8,	"SameSnError",                  "",    DT_Bool,    ACCESS_RO,  Save_None,  &gSysSnData.GlobalSnPara.sPlcSnSetPara.bSameSnError,                   0,               1,      "","",0,0},
	/*********************************************************** Global SN Para ***************************************************************/
	entry9001[8] = {0x10,	"Global Binding PartId",       "",     DT_Bool,    ACCESS_RO,  Save_None,  &gSysSnData.GlobalSnPara.bPartIdBindingSn,                             0,              1,      "","",0,0},
	entry9001[9] = {0x11,	"Global Binding Counter",      "",     DT_Bool,    ACCESS_RO,  Save_None,  &gSysSnData.GlobalSnPara.bCounterBindingSn,                            0,              1,      "","",0,0},
	entry9001[10] = {0x12,	"Global Binding OK/Nok",       "",     DT_Bool,    ACCESS_RO,  Save_None,  &gSysSnData.GlobalSnPara.bIdBindingResult,                             0,              1,      "","",0,0},
	entry9001[11] = {0x13,	"Global Use StationName",      "",     DT_Bool,    ACCESS_RO,  Save_None,  &gSysSnData.GlobalSnPara.bUseStationName,                              0,              1,      "","",0,0},
	entry9001[12] = {0x14,	"Global Use Profile Id",       "",     DT_Bool,    ACCESS_RO,  Save_None,  &gSysSnData.GlobalSnPara.bUseProfileId,                                0,              1,      "","",0,0},
	entry9001[13] = {0x15,	"Global Use Custom Header",    "",     DT_Bool,    ACCESS_RO,  Save_None,  &gSysSnData.GlobalSnPara.bUseCustomHeader,                             0,              1,      "","",0,0},
	entry9001[14] = {0x16,	"Global CustomHeader",         "",     DT_Str,     ACCESS_RO,  Save_None,  &gSysSnData.GlobalSnPara.sCustomHeader,                                0,              1,      "","",0,0 },
	entry9001[15] = {0x17,	"Global sSepparator",          "",     DT_Str,     ACCESS_RO,  Save_None,  &gSysSnData.GlobalSnPara.sSepparator,                                  0,              1,      "","",0,0 },
	entry9001[16] = {0x18,	"Global Y-M-D 1",              "",     DT_U8,      ACCESS_RO,  Save_None,  &gSysSnData.GlobalSnPara.DatasFormat[0],                               0,              3,      "","EnumList#0:None;1:Year;2:Month;3:Day",0,0},
	entry9001[17] = {0x19,	"Global Y-M-D 2",              "",     DT_U8,      ACCESS_RO,  Save_None,  &gSysSnData.GlobalSnPara.DatasFormat[1],                               0,              3,      "","EnumList#0:None;1:Year;2:Month;3:Day",0,0},
	entry9001[18] = {0x1A,	"Global Y-M-D 3",              "",     DT_U8,      ACCESS_RO,  Save_None,  &gSysSnData.GlobalSnPara.DatasFormat[2],                               0,              3,      "","EnumList#0:None;1:Year;2:Month;3:Day",0,0},
	entry9001[19] = {0x1B,	"Global Use Hour",             "",     DT_U8,      ACCESS_RO,  Save_None,  &gSysSnData.GlobalSnPara.DatasFormat[3],                               0,              6,      "","EnumList#0:None;4:HH;5:MN;6:SS",0,0},
	entry9001[20] = {0x1C,	"Global Use Minutes",          "",     DT_U8,      ACCESS_RO,  Save_None,  &gSysSnData.GlobalSnPara.DatasFormat[4],                               0,              6,      "","EnumList#0:None;4:HH;5:MN;6:SS",0,0},
	entry9001[21] = {0x1D,	"Global Use Second",           "",     DT_U8,      ACCESS_RO,  Save_None,  &gSysSnData.GlobalSnPara.DatasFormat[5],                               0,              6,      "","EnumList#0:None;4:HH;5:MN;6:SS",0,0 },
	entry9001[22] = {0x1E,	"Global Counter Digits",       "",     DT_U8,      ACCESS_RW,  Save_None,  &gSysSnData.GlobalSnPara.CounterDigits,                                0,              10,     "","",0,0 },
	entry9001[23] = {0x1F,	"Global Start Counter",        "",     DT_U64,     ACCESS_RW,  Save_None,  &gSysSnData.GlobalSnPara.StartWithCounter,                             0,      (uint64)4294967295,     "","",0,0 },
	entry9001[24] = {0x20,	"Global Counter Reset Time",   "",     DT_U8,      ACCESS_RO,  Save_None,  &gSysSnData.GlobalSnPara.eCounterResetTime,                            0,              4,      "","EnumList#0:None;1:Year;2:Month;3:Week;4:Day",0,0 },
	entry9001[25] = {0xA0,	"Plc PartId",                   "",    DT_Str,     ACCESS_RO,  Save_None,  &gSysSnData.RecPlcSn,                                                  0,              1,      "","",0,0 },
	entry9001[26] = {0xA1,	"Dealed Plc PartId",            "",    DT_Str,     ACCESS_RO,  Save_None,  &gSysSnData.PlcSn,                                                     0,              1,      "","",0,0 }
};

SDOEntryDesc entry9002[28] = {
	/************** Key  **  Name  **********************, Alias* DataType * ObjAccess **************************  pVar  *****************************************************************  Minimum  ********  Maximum ***/
	entry9002[0] = { 0x0,	"Profile Sn Para",              "",    DT_None,     ACCESS_RO,  Save_None,               0,                                                                        0,                   0,      "","",0,0 },
	entry9002[1] = { 0x1,	"Use Global Source",            "",    DT_Bool,     ACCESS_RO,  Save_None,  &gCurProfileSnData.bGlobalSource,                                        0,                   1,      "","",0,0 },
	entry9002[2] = { 0x2,	"Use Global Counter",           "",    DT_Bool,     ACCESS_RO,  Save_None,  &gCurProfileSnData.bUseGlobalCounter,                                    0,                   1,      "","",0,0 },
	entry9002[3] = { 0x3,	"Use Reset  Counter",           "",    DT_Bool,     ACCESS_RW,  Save_None,  &gCurProfileSnData.bResetCounter,                                        0,                   1,      "","",0,0 },
	entry9002[4] = { 0x4,	"Curent Profile Counter",       "",    DT_U64,      ACCESS_RO,  Save_None,  &gSysSnData.CurProfileSnCounter,                                         0,  (uint64)4294967295,      "","",0,0 },
	entry9002[5] = { 0x5,	"Illegal Char SwitchBlank",      "",   DT_Bool,     ACCESS_RO,  Save_None,  &gCurProfileSnData.SnPara.sPlcSnSetPara.bIllegalCharSwitchBlank,         0,                   1,      "","",0,0 },
	entry9002[6] = { 0x6,	"Same Sn BingTime",             "",    DT_Bool,     ACCESS_RO,  Save_None,  &gCurProfileSnData.SnPara.sPlcSnSetPara.bSameSnBingTime ,                0,                   1,      "","",0,0 },
	entry9002[7] = { 0x7,	"Null Sn Error",                "",    DT_Bool,     ACCESS_RO,  Save_None,  &gCurProfileSnData.SnPara.sPlcSnSetPara.bNullSnError,                    0,                   1,      "","",0,0 },
	entry9002[8] = { 0x8,	"SameSnError",                  "",    DT_Bool,     ACCESS_RO,  Save_None,  &gCurProfileSnData.SnPara.sPlcSnSetPara.bSameSnError,                    0,                   1,      "","",0,0 },

	/*********************************************************** Profile SN Para ***************************************************************/
	entry9002[9] = { 0x10,	"Profile Binding PartId",       "",     DT_Bool,    ACCESS_RO,  Save_None,  &gCurProfileSnData.SnPara.bPartIdBindingSn,                              0,                    1,      "","",0,0 },
	entry9002[10] = { 0x11,	"Profile Binding Counter",      "",     DT_Bool,    ACCESS_RO,  Save_None,  &gCurProfileSnData.SnPara.bCounterBindingSn,                             0,                    1,      "","",0,0 },
	entry9002[11] = { 0x12,	"Profile Binding OK/Nok",       "",     DT_Bool,    ACCESS_RO,  Save_None,  &gCurProfileSnData.SnPara.bIdBindingResult,                              0,                    1,      "","",0,0 },
	entry9002[12] = { 0x13,	"Profile Use StationName",      "",     DT_Bool,    ACCESS_RO,  Save_None,  &gCurProfileSnData.SnPara.bUseStationName,                               0,                    1,      "","",0,0 },
	entry9002[13] = { 0x14,	"Profile Use Profile Id",       "",     DT_Bool,    ACCESS_RO,  Save_None,  &gCurProfileSnData.SnPara.bUseProfileId,                                 0,                    1,      "","",0,0 },
	entry9002[14] = { 0x15,	"Profile Use Custom Header",    "",     DT_Bool,    ACCESS_RO,  Save_None,  &gCurProfileSnData.SnPara.bUseCustomHeader,                              0,                    1,      "","",0,0 },
	entry9002[15] = { 0x16,	"Profile CustomHeader",         "",     DT_Str,     ACCESS_RO,  Save_None,  &gCurProfileSnData.SnPara.sCustomHeader,                                 0,                    1,      "","",0,0 },
	entry9002[16] = { 0x17,	"Profile sSepparator",          "",     DT_Str,     ACCESS_RO,  Save_None,  &gCurProfileSnData.SnPara.sSepparator,                                   0,                    1,      "","",0,0 },
	entry9002[17] = { 0x18,	"Profile Y-M-D-1",              "",     DT_U8,      ACCESS_RO,  Save_None,  &gCurProfileSnData.SnPara.DatasFormat[0],                                0,                    3,      "","EnumList#0:None;1:Year;2:Month;3:Day",0,0 },
	entry9002[18] = { 0x19,	"Profile Y-M-D-2",              "",     DT_U8,      ACCESS_RO,  Save_None,  &gCurProfileSnData.SnPara.DatasFormat[1],                                0,                    3,      "","EnumList#0:None;1:Year;2:Month;3:Day",0,0 },
	entry9002[19] = { 0x1A,	"Profile Y-M-D-3",              "",     DT_U8,      ACCESS_RO,  Save_None,  &gCurProfileSnData.SnPara.DatasFormat[2],                                0,                    3,      "","EnumList#0:None;1:Year;2:Month;3:Day",0,0 },
	entry9002[20] = { 0x1B,	"Profile Use Hour",             "",     DT_U8,      ACCESS_RO,  Save_None,  &gCurProfileSnData.SnPara.DatasFormat[3],                                0,                    6,      "","EnumList#0:None;4:HH;5:MN;6:SS",0,0 },
	entry9002[21] = { 0x1C,	"Profile Use Minutes",          "",     DT_U8,      ACCESS_RO,  Save_None,  &gCurProfileSnData.SnPara.DatasFormat[4],                                0,                    6,      "","EnumList#0:None;4:HH;5:MN;6:SS",0,0 },
	entry9002[22] = { 0x1D,	"Profile Use Second",           "",     DT_U8  ,    ACCESS_RO,  Save_None,  &gCurProfileSnData.SnPara.DatasFormat[5],                                0,                    6,      "","EnumList#0:None;4:HH;5:MN;6:SS",0,0 },
	entry9002[23] = { 0x1E,	"Profile Counter Digits",       "",     DT_U8,      ACCESS_RW,  Save_None,  &gCurProfileSnData.SnPara.CounterDigits,                                 0,                   10,      "","",0,0 },
	entry9002[24] = { 0x1F,	"Profile Start Counter",        "",     DT_U64,     ACCESS_RW,  Save_None,  &gCurProfileSnData.SnPara.StartWithCounter,                              0,   (uint64)4294967295,      "","",0,0 },
	entry9002[25] = { 0x20,	"Profile Counter Reset Time",   "",     DT_U8,      ACCESS_RO,  Save_None,  &gCurProfileSnData.SnPara.eCounterResetTime,                             0,                   4,       "","EnumList#0:None;1:Year;2:Month;3:Week;4:Day",0,0 },
	entry9002[26] = { 0x21,	"Profile MeasureStart Type",    "",     DT_U8,      ACCESS_RO,  Save_None,  &pMeasureType.MeasureStart,												 0,                   4,       "","EnumList#1:Manual0p;2:PLC_I0;3:xTrigger;4:yTrigger",0,0 },
	entry9002[27] = { 0x22,	"Profile MeasureStop Typ",      "",     DT_U8,      ACCESS_RO,  Save_None,  &pMeasureType.MeasureStop,												 0,                   4,       "","EnumList#1:Manual0p;2:PLC_I0;3:xTrigger;4:yTrigger;5:TimeMode;6:CurveReturn",0,0 },

};

//曲线设置参数组
SDOEntryDesc entryA000[3 + 4 * MAX_CHARTS_NUMBER + 2] =
{
	/************** Key  **  Name  **********************, Alias* DataType * ObjAccess ****  pVar  ******************************************************  Minimum  ******  Maximum ***/
	entryA000[0] = { 0x0,	"Charts Info",              "",     DT_None,    ACCESS_RO,  Save_None,  0,                                                            0,      0,      "","",0,0 },
	entryA000[1] = { 0x01,	"Sample Info",              "",     DT_I16,     ACCESS_RW,  Save_User,  &psChartsData->eSampleType,                                   0,      255,      "","EnumList#1:4000Points/S;2:2000Points/S;3:1000Points/S;4:500Points/S;5:100Points/S;6:50Points/S;7:10Points/S;10:1mm;11:0.1mm;12:0.01mm;20:1KN;21:0.1KN;22:0.01KN",0,0 },
	entryA000[2] = { 0x10,	"Charts Count",             "",     DT_U8,      ACCESS_RW,  Save_User,  &psChartsData->iChartCount,                                   0,      10,      "","",0,0 },


	entryA000[3] = { 0x11,	"Charts1 X Type",             "",   DT_I16,     ACCESS_RW,  Save_User,  &psChartsData->sChart[0].sChartSetInfo.eXChannelType,         0,      255,      "","EnumList#0:UnSet;1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",0,0 },
	entryA000[4] = { 0x12,	"Charts1 X Name",             "",   DT_Str,     ACCESS_RW,  Save_User,  &psChartsData->sChart[0].sChartSetInfo.sXChannelName,         0,      1,      "","",0,0 },
	entryA000[5] = { 0x13,	"Charts1 Y Type",             "",   DT_I16,     ACCESS_RW,  Save_User,  &psChartsData->sChart[0].sChartSetInfo.eYChannelType,         0,      255,      "","EnumList#0:UnSet;1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",0,0 },
	entryA000[6] = { 0x14,	"Charts1 Y Name",             "",   DT_Str,     ACCESS_RW,  Save_User,  &psChartsData->sChart[0].sChartSetInfo.sYChannelName,         0,      1,      "","",0,0 },

	//entryA000[7] = { 0x21,	"Charts2 X Type",             "",   DT_I16,     ACCESS_RW,  Save_User,  &psChartsData->sChart[1].sChartSetInfo.eXChannelType,         0,      255,      "","EnumList#0:UnSet;1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",0,0 };
	//entryA000[8] = { 0x22,	"Charts2 X Name",             "",   DT_Str,     ACCESS_RW,  Save_User,  &psChartsData->sChart[1].sChartSetInfo.sXChannelName,         0,      1,      "","",0,0 };
	//entryA000[9] = { 0x23,	"Charts2 Y Type",             "",   DT_I16,     ACCESS_RW,  Save_User,  &psChartsData->sChart[1].sChartSetInfo.eYChannelType,         0,      255,      "","EnumList#0:UnSet;1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",0,0 };
	//entryA000[10] = { 0x24,	"Charts2 Y Name",             "",   DT_Str,     ACCESS_RW,  Save_User,  &psChartsData->sChart[1].sChartSetInfo.sYChannelName,         0,      1,      "","",0,0 };

	//entryA000[11] = { 0x31,	"Charts3 X Type",             "",   DT_I16,     ACCESS_RW,  Save_User,  &psChartsData->sChart[2].sChartSetInfo.eXChannelType,         0,      255,      "","EnumList#0:UnSet;1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",0,0 };
	//entryA000[12] = { 0x32,	"Charts3 X Name",             "",   DT_Str,     ACCESS_RW,  Save_User,  &psChartsData->sChart[2].sChartSetInfo.sXChannelName,         0,      1,      "","",0,0 };
	//entryA000[13] = { 0x33,	"Charts3 Y Type",             "",   DT_I16,     ACCESS_RW,  Save_User,  &psChartsData->sChart[2].sChartSetInfo.eYChannelType,         0,      255,      "","EnumList#0:UnSet;1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",0,0 };
	//entryA000[14] = { 0x34,	"Charts3 Y Name",             "",   DT_Str,     ACCESS_RW,  Save_User,  &psChartsData->sChart[2].sChartSetInfo.sYChannelName,         0,      1,      "","",0,0 };

	//entryA000[15] = { 0x41,	"Charts4 X Type",             "",   DT_I16,     ACCESS_RW,  Save_User,  &psChartsData->sChart[3].sChartSetInfo.eXChannelType,         0,      255,      "","EnumList#0:UnSet;1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",0,0 };
	//entryA000[16] = { 0x42,	"Charts4 X Name",             "",   DT_Str,     ACCESS_RW,  Save_User,  &psChartsData->sChart[3].sChartSetInfo.sXChannelName,         0,      1,      "","",0,0 };
	//entryA000[17] = { 0x43,	"Charts4 Y Type",             "",   DT_I16,     ACCESS_RW,  Save_User,  &psChartsData->sChart[3].sChartSetInfo.eYChannelType,         0,      255,      "","EnumList#0:UnSet;1:SystemPara;2:MotorEncode;3:StrainGauge;4:Potentiometer;5:Voltage;6:Current;7:RTD",0,0 };
	//entryA000[18] = { 0x44,	"Charts4 Y Name",             "",   DT_Str,     ACCESS_RW,  Save_User,  &psChartsData->sChart[3].sChartSetInfo.sYChannelName,         0,      1,      "","",0,0 };
	// 新增的两个参数，从0xa0开始
	entryA000[7] = { 0xa0,	"Curve Truncation Visible",    "",   DT_Bool,    ACCESS_RW,  Save_User,  &bCurveTruncationVisible,                      0,      1,      "","",0,0 },
	entryA000[8] = { 0xa1,	"Turncation Point Index",      "",   DT_U32,     ACCESS_RO,  Save_None,  &TurncationPointIndex,                         0,      0xFFFFFFFF, "","",0,0 }
};

//数据交换  文件服务器
SDOEntryDesc entryC000[4] = {
	/*Key  **  Name  **********************, Alias* DataType * ObjAccess ****  pVar  ******************************  Minimum  ******  Maximum ***/
	entryC000[0] = {0x0,	"Data Server Para",              "",   DT_None,   ACCESS_RO,  Save_None,  0,                              0,      0,      "","",0,0},
	entryC000[1] = {0x1,	"Data Server Active State",      "",   DT_Bool,   ACCESS_RO,  Save_User,   &bServerActivedflag,           0,      1,      "","",0,0},
	entryC000[2] = {0x2,	"Data Server Connect State",     "",   DT_Bool,   ACCESS_RO,  Save_User,   &bServerConnectflag,           0,      1,      "","",0,0},
	entryC000[3] = {0x3,	"Data Server Connect Test",      "",   DT_Bool,   ACCESS_RW,  Save_User,   &bServerConnectTest,           0,      1,      "","",0,0}
};

//数据交换  总线
SDOEntryDesc entryC001[7] = {
	/*************** Key  **  Name  **********************, Alias* DataType * ObjAccess ****  pVar  ******************************  Minimum  ******  Maximum ***/
	entryC001[0] = { 0x0,	"FieldBus Para",                "",   DT_None,  ACCESS_RO,  Save_None,  0,                                  0,      0,      "","",0,0 },
	entryC001[1] = { 0x1,	"FieldBus Connect State",       "",   DT_Bool,  ACCESS_RO,  Save_None,  &pSysShareData->bFiledBusOk,                       0,      1,      "","",0,0 },
	entryC001[2] = { 0x2,	"Fieldbus Type",                "",   DT_U8,    ACCESS_RO,  Save_None,  &pSysShareData->eFieldbusType,                     0,      255,     "","EnumList#0:None;1:Profinet;2:Ethercat;3:Ethernet_IP;4:CC_LINK;5:DeviceNet;6:Modbus_TCP;7:Profibus;8:CANopen;9:CC_Link_IE_Field;10:Powerlink;11:BACnet_IP;12:CommonEthernet;255:UnknownType",         0,            0 },
	entryC001[3] = { 0x3,	"CC-Link  NodeAddress",         "",   DT_U8,    ACCESS_RO,  Save_User,  &pSysShareData->sCCLConfigInfo.NodeAddress,        1,      64,      "","",0,0 },
	entryC001[4] = { 0x4,	"CC-Link  Baudrate",            "",   DT_U8,    ACCESS_RO,  Save_User,  &pSysShareData->sCCLConfigInfo.Baudrate,           0,      10,      "","EnumList#0:156kbps;1:625kbps;2:2.5Mbps;3:5Mbps;4:10Mbps",0,0 },
	entryC001[5] = { 0x5,	"CC-Link  OccupiedStations",    "",   DT_U8,    ACCESS_RO,  Save_User,  &pSysShareData->sCCLConfigInfo.OccupiedStations,   1,      4,      "","",0,0 },
	entryC001[6] = { 0x6,	"CC-Link  ExtensionCycles",     "",   DT_U8,    ACCESS_RO,  Save_User,  &pSysShareData->sCCLConfigInfo.ExtensionCycles,    1,      8,      "","",0,0 }
};

//数据交换  总线
SDOEntryDesc entryC002[2] = {
	/*************** Key  **  Name  **********************, Alias* DataType * ObjAccess ****  pVar  ******************************  Minimum  ******  Maximum ***/
	entryC001[0] = { 0x0,	"Premession Para",              "",   DT_None,  ACCESS_RO,  Save_None,  0,                                  0,      0,      "","",0,0 },
	entryC001[1] = { 0x1,	"Premession Set",               "",   DT_U16,   ACCESS_RW,  Save_None,  &PremissionSet,                     0,   65535,      "","",0,0 },
};

SDOEntryGroup entryGroup[ENTRY_GROUP_NUMBER];
void InitSdoEntryDes()
{
	InitGlobalVarSdo();        //0xB001
	InitLocalVarSdo();         //0xB002

	InitEoResultSdo();         //0xB010        
	InitEoPositionSdo();       //0xB011

	uint8 indexGroup = 0;
	entryGroup[indexGroup] = { 0x1000,	                         entry1000,                                  arraySize(entry1000) };
	indexGroup++;
	entryGroup[indexGroup] = { 0x1100,	                         entry1100,                                  arraySize(entry1100) };
	indexGroup++;
	entryGroup[indexGroup] = { 0x1200,	                         entry1200,                                  arraySize(entry1200) };
	indexGroup++;
	entryGroup[indexGroup] = { 0x1A00,	                         entry1A00,                                  arraySize(entry1A00) };       //上位机交互指令
	indexGroup++;
	entryGroup[indexGroup] = { 0x1F00,	                         entry1F00,                                  arraySize(entry1F00) };       //系统状态
	indexGroup++;

	entryGroup[indexGroup] = { 0x3011,	                         entry3011,                                  arraySize(entry3011) };       //客户端状态字
	indexGroup++;
	entryGroup[indexGroup] = { 0x2012,	                         entry2012,                                  arraySize(entry2012) };       //客户端控制字
	indexGroup++;
	entryGroup[indexGroup] = { 0x2013,	                         entry2013,                                  arraySize(entry2013) };       //IO控制字
	indexGroup++;
	entryGroup[indexGroup] = { 0x2014,	                         entry2014,                                  arraySize(entry2014) };       //总线控制字
	indexGroup++;
	entryGroup[indexGroup] = { 0x3012,                            entry3012,                                  arraySize(entry3012) };      //总线状态
	indexGroup++;
	entryGroup[indexGroup] = { 0x3013,                             entry3013,                                  arraySize(entry3013) };      //IO状态字
	indexGroup++;
	entryGroup[indexGroup] = { 0x4001,      ChannelPeakVallyPara.SensorsPara,      (uint8)(ChannelPeakVallyPara.iSensorsParaCount + 1) };    //曲线峰谷值
	indexGroup++;
	entryGroup[indexGroup] = { 0x4002,                             entry4002,                                  arraySize(entry4002) };      //曲线结果数据
	indexGroup++;
	entryGroup[indexGroup] = { 0x4003,                             entry4003,                                  arraySize(entry4003) };      //折返点结果数据
	indexGroup++;
	entryGroup[indexGroup] = { 0x5000,           SensorsAvalPara.SensorsPara,        (uint8)(SensorsAvalPara.iSensorsParaCount + 1) };       //设备实际 可用参数 = 工厂参数和设置参数的交集   动态sdo组
	//entryGroup[indexGroup] = { 0x5000,                           entry5000,                                 arraySize(entry5000) };       //设备实际 可用参数 = 工厂参数和设置参数的交集   动态sdo组
	indexGroup++;
#if (SYSTEM_MOTION_MODE == 1) 
	entryGroup[indexGroup] = { 0x5001,        SensorsFactoryPara.SensorsPara,        (uint8)(SensorsFactoryPara.iSensorsParaCount + 1) };    //设备出厂 参数    动态sdo组
	//entryGroup[indexGroup] = { 0x5001,                           entry5001,                                 arraySize(entry5001) };       //设备实际 可用参数 = 工厂参数和设置参数的交集   动态sdo组
	indexGroup++;
#endif
	entryGroup[indexGroup] = { 0x5002,            SensorsSetPara.SensorsPara,        (uint8)(SensorsSetPara.iSensorsParaCount + 1) };        //设备设置 参数    动态sdo组
	//entryGroup[indexGroup] = { 0x5002,                           entry5002,                                 arraySize(entry5002) };       //设备实际 可用参数 = 工厂参数和设置参数的交集   动态sdo组
	indexGroup++;
	entryGroup[indexGroup] = { 0x5010,                             entry5010,                                 arraySize(entry5010) };        //驱动器参数全览
	indexGroup++;
	entryGroup[indexGroup] = { 0x5011,                             entry5011,                                 arraySize(entry5011) };        //驱动器 1 参数及设置
	indexGroup++;
	entryGroup[indexGroup] = { 0x5050,                            entry5050,                                 arraySize(entry5050) };        //压力传感器
	indexGroup++;
	entryGroup[indexGroup] = { 0x5051,	                         entry5051,                                 arraySize(entry5051) };        //压力传感器1 参数及设置
	indexGroup++;
	entryGroup[indexGroup] = { 0x5060,	                         entry5060,                                 arraySize(entry5060) };        //电位计参数全览 
	indexGroup++;
	entryGroup[indexGroup] = { 0x5061,	                         entry5061,                                 arraySize(entry5061) };        //电位计1 参数及设置
	indexGroup++;
	entryGroup[indexGroup] = { 0x50FF,	                         entry50FF,                                 arraySize(entry50FF) };       //系统保护参数
	indexGroup++;
	entryGroup[indexGroup] = { 0x6000,	                         entry6000,                                 arraySize(entry6000) };        //统计数据、存储在数据库中，此处Sdo中数据给上位机读取展示
	indexGroup++;
	entryGroup[indexGroup] = { 0x7001,	                         entry7001,                                 arraySize(entry7001) };        //点动参数
	indexGroup++;
	entryGroup[indexGroup] = { 0x8001,	                         entry8001,                                 arraySize(entry8001) };        //sequecne参数
	indexGroup++;
	entryGroup[indexGroup] = { 0x9001,	                         entry9001,                                 arraySize(entry9001) };        //SN 全局参数
	indexGroup++;
	entryGroup[indexGroup] = { 0x9002,	                         entry9002,                                 arraySize(entry9002) };        //SN 工艺参数
	indexGroup++;
	entryGroup[indexGroup] = { 0xA000,                            entryA000,                                 arraySize(entryA000) };        //曲线设置参数组
	indexGroup++;
	entryGroup[indexGroup] = { 0xB001,                       entryGlobalVar,                                 arraySize(entryGlobalVar) };   //动态全局变量  用于作为总线配置
	indexGroup++;
	entryGroup[indexGroup] = { 0xB002,                        entryLocalVar,                                 arraySize(entryLocalVar) };    //动态局部变量  用于作为总线配置
	indexGroup++;
	entryGroup[indexGroup] = { 0xB010,                        entryEoResult,                                 arraySize(entryEoResult) };    //Eo 结果  用于作为总线配置
	indexGroup++;
	entryGroup[indexGroup] = { 0xB011,                      entryEoPosition,                                 arraySize(entryEoPosition) };  //Eo 位置  用于作为总线配置
	indexGroup++;
	entryGroup[indexGroup] = { 0xC000,                            entryC000,                                 arraySize(entryC000) };       //文件服务器
	indexGroup++;
	entryGroup[indexGroup] = { 0xC001,                            entryC001,                                 arraySize(entryC001) };       //数据交换  总线
	indexGroup++;
	entryGroup[indexGroup] = { 0xC002,                            entryC002,                                 arraySize(entryC002) };
	indexGroup++;
	if (indexGroup != ENTRY_GROUP_NUMBER)
		dbgPrint(1, 0, "!!!!!!!!!!!!!!!!!!!!! indexGroup[%d]!= ENTRY_GROUP_NUMBER[%d]\n", indexGroup, ENTRY_GROUP_NUMBER);
	//else
	//    dbgPrint(1, 0, "!!!!!!!!!!!!!!!!!!!!! indexGroup[%d] == ENTRY_GROUP_NUMBER[%d]\n", indexGroup, ENTRY_GROUP_NUMBER);
}